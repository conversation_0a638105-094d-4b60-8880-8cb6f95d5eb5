use anyhow::{Result, anyhow};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::Instruction,
    message::Message,
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use std::str::FromStr;
use std::sync::Arc;
use crate::shared::types::WalletConfig;
use crate::protocols::bonk::utils::parser::BonkTradingData;
use crate::blockhash_service::BlockhashService;
use tracing::{info, debug, error, warn};

// 复用bonk参考代码的常量
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
const RAYDIUM_LAUNCHPAD_PROGRAM: &str = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj";

pub struct BonkTransactionBuilder {
    wallet: Arc<Keypair>,
    blockhash_service: Arc<BlockhashService>,
    rpc_client: Arc<RpcClient>,
}

impl BonkTransactionBuilder {
    pub fn new(
        wallet: Arc<Keypair>, 
        blockhash_service: Arc<BlockhashService>,
        rpc_url: &str
    ) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new_with_commitment(
            rpc_url.to_string(),
            CommitmentConfig::confirmed(),
        ));
        
        Ok(Self {
            wallet,
            blockhash_service,
            rpc_client,
        })
    }
    
    /// 构建Bonk买入交易
    pub fn build_buy_transaction(
        &self,
        bonk_data: &BonkTradingData,
        buy_amount_sol: f64,
        wallet_config: &WalletConfig,
    ) -> Result<Transaction> {
        info!("开始构建Bonk买入交易: {} SOL", buy_amount_sol);
        
        // 完全复用bonk参考代码的execute_buy逻辑
        let wsol_amount = (buy_amount_sol * 1_000_000_000.0) as u64; // 转换为lamports
        
        // 基础参数
        let pool_id = Pubkey::from_str(&bonk_data.pool_state)
            .map_err(|e| anyhow!("无效的pool_state地址: {}", e))?;
        let token_mint = Pubkey::from_str(&bonk_data.mint_address)
            .map_err(|e| anyhow!("无效的mint地址: {}", e))?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        
        // 从Redis直接获取的金库地址
        let base_vault = Pubkey::from_str(&bonk_data.pool_base_vault)
            .map_err(|e| anyhow!("无效的pool_base_vault地址: {}", e))?;
        let quote_vault = Pubkey::from_str(&bonk_data.pool_quote_vault)
            .map_err(|e| anyhow!("无效的pool_quote_vault地址: {}", e))?;
        
        // 计算池权限地址
        let (pool_authority, _) = self.find_pool_authority(&pool_id);
        
        // 计算用户的关联Token账户地址
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &wsol_mint
        );
        let user_token_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &token_mint
        );
        
        // 计算期望输出（基于当前价格和滑点）
        let minimum_amount_out = if bonk_data.amount_out > 0 {
            let ratio = wsol_amount as f64 / bonk_data.amount_in as f64;
            let expected_out = ratio * bonk_data.amount_out as f64;
            let slippage = wallet_config.slippage_percentage / 100.0;
            (expected_out * (1.0 - slippage)) as u64
        } else {
            1 // 最小输出
        };
        
        debug!("Bonk交易参数: wsol_amount={}, minimum_amount_out={}, slippage={}%", 
            wsol_amount, minimum_amount_out, wallet_config.slippage_percentage);
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        let compute_limit = wallet_config.compute_unit_limit;
        let priority_fee = wallet_config.priority_fee;
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(priority_fee));
        
        // 2. 创建代币账户（幂等操作）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &wsol_mint,
                &spl_token::id(),
            )
        );
        
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &token_mint,
                &spl_token::id(),
            )
        );
        
        // 3. SOL转到WSOL账户
        instructions.push(system_instruction::transfer(
            &self.wallet.pubkey(),
            &user_wsol_account,
            wsol_amount,
        ));
        
        // 4. 同步WSOL账户
        instructions.push(spl_token::instruction::sync_native(
            &spl_token::id(), 
            &user_wsol_account
        )?);
        
        // 5. 构建Raydium Launchpad交易指令
        let raydium_launchpad_program = Pubkey::from_str(RAYDIUM_LAUNCHPAD_PROGRAM)?;
        
        // 构建buy_exact_in指令数据
        let mut swap_data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // buy_exact_in discriminator
        swap_data.extend_from_slice(&wsol_amount.to_le_bytes());
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes());
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 计算必要的PDA账户
        let (authority, _) = Pubkey::find_program_address(
            &[b"vault_auth_seed"], 
            &raydium_launchpad_program
        );
        let (event_authority, _) = Pubkey::find_program_address(
            &[b"__event_authority"], 
            &raydium_launchpad_program
        );
        
        // 使用固定配置地址（基于bonk参考代码）
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        // 构建15个账户列表（基于bonk参考代码）
        let accounts = vec![
            solana_sdk::instruction::AccountMeta::new(self.wallet.pubkey(), true), // 0: user (signer)
            solana_sdk::instruction::AccountMeta::new(pool_id, false), // 1: pool_state
            solana_sdk::instruction::AccountMeta::new_readonly(pool_authority, false), // 2: pool_authority
            solana_sdk::instruction::AccountMeta::new(user_wsol_account, false), // 3: user_wsol_account
            solana_sdk::instruction::AccountMeta::new(user_token_account, false), // 4: user_token_account
            solana_sdk::instruction::AccountMeta::new(base_vault, false), // 5: pool_base_vault
            solana_sdk::instruction::AccountMeta::new(quote_vault, false), // 6: pool_quote_vault
            solana_sdk::instruction::AccountMeta::new_readonly(wsol_mint, false), // 7: wsol_mint
            solana_sdk::instruction::AccountMeta::new_readonly(token_mint, false), // 8: token_mint
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false), // 9: token_program
            solana_sdk::instruction::AccountMeta::new_readonly(spl_associated_token_account::id(), false), // 10: associated_token_program
            solana_sdk::instruction::AccountMeta::new_readonly(solana_sdk::system_program::id(), false), // 11: system_program
            solana_sdk::instruction::AccountMeta::new_readonly(global_config, false), // 12: global_config
            solana_sdk::instruction::AccountMeta::new_readonly(platform_config, false), // 13: platform_config
            solana_sdk::instruction::AccountMeta::new_readonly(event_authority, false), // 14: event_authority
        ];
        
        instructions.push(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        });
        
        // 6. 关闭WSOL账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.wallet.pubkey(),
            &self.wallet.pubkey(),
            &[],
        )?);
        
        // 构建最终交易
        let recent_blockhash = self.blockhash_service.get_latest_blockhash()
            .ok_or_else(|| anyhow!("无法获取最新区块哈希"))?;
        
        let message = Message::new(&instructions, Some(&self.wallet.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&*self.wallet], recent_blockhash);
        
        info!("Bonk买入交易构建完成: {} SOL -> {} tokens (最小输出)", 
            buy_amount_sol, minimum_amount_out);
        
        Ok(transaction)
    }
    
    /// 计算池权限地址
    fn find_pool_authority(&self, pool_id: &Pubkey) -> (Pubkey, u8) {
        let program_id = Pubkey::from_str(RAYDIUM_LAUNCHPAD_PROGRAM).unwrap();
        Pubkey::find_program_address(&[&pool_id.to_bytes()[..32]], &program_id)
    }
    
    /// 获取钱包公钥
    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }
    
    /// 验证交易参数
    pub fn validate_bonk_trade_params(
        &self,
        bonk_data: &BonkTradingData,
        buy_amount_sol: f64,
    ) -> Result<()> {
        // 验证基本参数
        if buy_amount_sol <= 0.0 {
            return Err(anyhow!("买入金额必须大于0"));
        }
        
        if buy_amount_sol > 10.0 {
            return Err(anyhow!("买入金额不能超过10 SOL"));
        }
        
        // 验证地址格式
        Pubkey::from_str(&bonk_data.pool_state)
            .map_err(|_| anyhow!("无效的pool_state地址"))?;
        Pubkey::from_str(&bonk_data.mint_address)
            .map_err(|_| anyhow!("无效的mint地址"))?;
        Pubkey::from_str(&bonk_data.pool_base_vault)
            .map_err(|_| anyhow!("无效的pool_base_vault地址"))?;
        Pubkey::from_str(&bonk_data.pool_quote_vault)
            .map_err(|_| anyhow!("无效的pool_quote_vault地址"))?;
        
        // 验证交易方向
        if bonk_data.trade_direction != "Buy" {
            return Err(anyhow!("只支持买入交易"));
        }
        
        // 验证价格合理性
        if bonk_data.price_after <= 0.0 {
            return Err(anyhow!("无效的交易价格"));
        }
        
        Ok(())
    }
}