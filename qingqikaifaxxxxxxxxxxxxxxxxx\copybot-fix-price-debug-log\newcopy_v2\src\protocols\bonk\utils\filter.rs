use crate::shared::types::{HotPathTrade, WalletConfig, FollowMode};
use serde_derive::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// Bonk协议主筛选配置，复用pump的结构
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct BonkFilterConfig {
    pub allowed_creators: HashSet<String>,
    pub block_list_mint: HashSet<String>,
    pub min_price: f64,
    pub max_price: f64,
    /// 存储每个受监控钱包的计算配置，复用pump的WalletConfig
    pub wallet_configs: HashMap<String, WalletConfig>,
}

/// 用于控制Bonk筛选器配置更新的命令，复用pump的结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BonkFilterCommand {
    AddCreator(String),
    RemoveCreator(String),
    AddToBlockList(String),
    RemoveFromBlockList(String),
    SetMinPrice(f64),
    SetMaxPrice(f64),
    UpdateWallet(WalletConfig),
    RemoveWallet(String),
}

impl BonkFilterConfig {
    /// 根据命令应用更改，返回一个新的配置实例
    pub fn apply_command(mut self, command: BonkFilterCommand) -> Self {
        match command {
            BonkFilterCommand::AddCreator(creator) => {
                self.allowed_creators.insert(creator);
            }
            BonkFilterCommand::RemoveCreator(creator) => {
                self.allowed_creators.remove(&creator);
            }
            BonkFilterCommand::AddToBlockList(mint) => {
                self.block_list_mint.insert(mint);
            }
            BonkFilterCommand::RemoveFromBlockList(mint) => {
                self.block_list_mint.remove(&mint);
            }
            BonkFilterCommand::SetMinPrice(price) => {
                self.min_price = price;
            }
            BonkFilterCommand::SetMaxPrice(price) => {
                self.max_price = price;
            }
            BonkFilterCommand::UpdateWallet(wallet_config) => {
                self.wallet_configs.insert(wallet_config.wallet_address.clone(), wallet_config);
            }
            BonkFilterCommand::RemoveWallet(address) => {
                self.wallet_configs.remove(&address);
            }
        }
        self
    }
}

/// Bonk Filter 结构体，复用pump的逻辑
#[derive(Debug, Default, Clone)]
pub struct BonkFilter {
    pub config: BonkFilterConfig,
}

impl BonkFilter {
    pub fn new(config: BonkFilterConfig) -> Self {
        Self { config }
    }

    /// 通用筛选逻辑，检查交易是否来自跟踪的钱包
    pub fn get_config_if_tracked(&self, trade: &HotPathTrade) -> Option<&WalletConfig> {
        // 1. 检查交易价格是否有效
        if trade.price <= 0.0 {
            return None;
        }
        
        // 2. 全局价格筛选
        if self.config.min_price > 0.0 && trade.price < self.config.min_price {
            return None;
        }
        if self.config.max_price > 0.0 && trade.price > self.config.max_price {
            return None;
        }

        // 3. 检查签名者地址，并获取其专属配置
        let config = self.config.wallet_configs.get(&trade.signer)?;

        // 4. 检查该配置是否激活
        if !config.is_active {
            return None;
        }

        Some(config)
    }

    /// 核心购买金额计算逻辑，复用pump的逻辑
    pub fn check_and_get_buy_details(&self, trade: &HotPathTrade, config: &WalletConfig) -> Option<f64> {
        // 个人钱包价格筛选
        if let Some(min_multiplier) = config.min_price_multiplier {
            if min_multiplier > 0.0 && trade.price < min_multiplier {
                return None;
            }
        }

        if let Some(max_multiplier) = config.max_price_multiplier {
            if max_multiplier > 0.0 && trade.price > max_multiplier {
                return None;
            }
        }

        // 购买金额筛选
        if let Some(min_sol) = config.sol_amount_min {
            if trade.sol_cost < min_sol {
                return None;
            }
        }
        if let Some(max_sol) = config.sol_amount_max {
            if trade.sol_cost > max_sol {
                return None;
            }
        }

        // 根据跟单模式计算跟单金额
        let buy_amount_sol = match config.follow_mode {
            FollowMode::Percentage => {
                match config.follow_percentage {
                    Some(pct) => trade.sol_cost * (pct / 100.0),
                    None => return None,
                }
            },
            FollowMode::FixedAmount => {
                match config.fixed_follow_amount_sol {
                    Some(amount) => amount,
                    None => return None,
                }
            }
        };

        if buy_amount_sol <= 0.0 {
            return None;
        }

        Some(buy_amount_sol)
    }
}