# Product Overview

## Trading System - 20-Minute Self-Destruct Version

This is a comprehensive cryptocurrency trading system focused on Solana-based protocols, specifically designed for automated trading and copy-trading functionality.

### Core Features
- **Multi-Protocol Support**: Supports PumpFun and Bonk protocols with extensible architecture for additional protocols
- **Copy Trading**: Automated following of successful traders with configurable parameters
- **Real-time Processing**: High-performance Redis-based message processing with microsecond-level protocol detection
- **Web Interface**: React-based admin frontend for configuration and monitoring
- **Containerized Deployment**: Docker-based all-in-one deployment with 20-minute self-destruct capability

### Target Users
- Cryptocurrency traders looking for automated trading solutions
- Users interested in copy-trading successful Solana traders
- Developers building on Solana DeFi protocols

### Key Value Propositions
- Zero-latency trade execution
- Multi-protocol support with protocol-agnostic architecture
- Easy deployment with Docker
- Comprehensive monitoring and configuration interface