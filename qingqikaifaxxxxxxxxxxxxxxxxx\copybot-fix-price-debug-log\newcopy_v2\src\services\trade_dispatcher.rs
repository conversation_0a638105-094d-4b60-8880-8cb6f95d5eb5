/// 交易分发器模块
/// 
/// 负责订阅Redis，解析交易，然后分发给对应的协议处理器

use anyhow::{anyhow, Result};
use futures_util::stream::StreamExt;
use tracing::{info, debug, error, warn};
use std::sync::Arc;
use tokio::sync::mpsc;

use crate::protocols::pump::parse_trades_from_redis_bytes;
use crate::protocols::bonk::core::{parse_bonk_data_from_redis, BonkProcessor};
use crate::shared::types::HotPathTrade;
use crate::protocols::pump::Filter;
use arc_swap::ArcSwap;
use crate::protocols::pump::TransactionBuilder;
use solana_sdk::pubkey::Pubkey;

const TRADES_CHANNEL: &str = "trades_channel";

/// 交易分发器 - 订阅Redis并分发
pub struct TradeDispatcher {
    redis_url: String,
    user_wallet_pubkey: Pubkey,
    /// 发送给Pump处理器的通道
    pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
    /// Bonk处理器实例（直接调用）
    bonk_processor: Option<Arc<BonkProcessor>>,
}

impl TradeDispatcher {
    /// 创建新的交易分发器
    pub fn new(
        redis_url: &str,
        transaction_builder: Arc<TransactionBuilder>,
        pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
        bonk_processor: Option<Arc<BonkProcessor>>,
    ) -> Self {
        let user_wallet_pubkey = transaction_builder.get_wallet_pubkey();

        Self {
            redis_url: redis_url.to_string(),
            user_wallet_pubkey,
            pump_tx,
            bonk_processor,
        }
    }
    
    /// 启动分发器 - 统一订阅Redis并智能分发
    pub async fn start(&self, filter: Arc<ArcSwap<Filter>>) -> Result<()> {
        let client = redis::Client::open(self.redis_url.as_ref())
            .map_err(|e| anyhow!("创建Redis客户端失败: {}", e))?;

        let mut pubsub_conn = client
            .get_async_pubsub()
            .await
            .map_err(|e| anyhow!("获取PubSub连接失败: {}", e))?;

        pubsub_conn
            .subscribe(TRADES_CHANNEL)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", TRADES_CHANNEL, e))?;

        info!("交易分发器已成功订阅Redis频道: {}", TRADES_CHANNEL);

        let mut msg_stream = pubsub_conn.on_message();
        
        while let Some(msg) = msg_stream.next().await {
            let payload: Vec<u8> = msg.get_payload_bytes().to_vec();

            // 智能分发：同时尝试解析Pump和Bonk协议
            self.dispatch_trades(&payload, &filter).await;
        }
        
        warn!("交易分发器消息流已结束");
        Ok(())
    }
    
    /// 智能分发交易数据到对应的协议处理器
    async fn dispatch_trades(&self, payload: &[u8], filter: &Arc<ArcSwap<Filter>>) {
        // 尝试解析为Pump协议交易
        let pump_trades = parse_trades_from_redis_bytes(payload, &self.user_wallet_pubkey);
        let pump_count = pump_trades.len();
        let pump_has_trades = !pump_trades.is_empty();
        
        if pump_has_trades {
            debug!("分发器解析到 {} 笔Pump交易", pump_count);
            if let Err(e) = self.pump_tx.send(pump_trades) {
                error!("发送交易到Pump处理器失败: {}", e);
            }
        }
        
        // 尝试解析为Bonk协议交易
        let bonk_has_trades = if let Some(bonk_data) = parse_bonk_data_from_redis(payload) {
            // 直接调用Bonk处理器进行真实处理，使用真实的Filter配置
            if let Some(bonk_processor) = &self.bonk_processor {
                // 获取当前Filter配置
                let current_filter = filter.load();

                // 简单检查：查找signer对应的钱包配置
                if let Some(config) = current_filter.config.wallet_configs.get(&bonk_data.signer) {
                    if config.is_active {
                        info!("🎯 找到匹配的Bonk钱包配置: {}", config.wallet_address);

                        match bonk_processor.process_redis_message(payload, config).await {
                            Ok(_) => {
                                info!("✅ Bonk交易处理完成");
                                true
                            }
                            Err(e) => {
                                error!("❌ Bonk交易处理失败: {}", e);
                                false
                            }
                        }
                    } else {
                        debug!("🔍 Bonk钱包配置未激活，跳过交易");
                        false
                    }
                } else {
                    debug!("🔍 未找到匹配的Bonk钱包配置，跳过交易");
                    false
                }
            } else {
                warn!("⚠️ Bonk处理器未初始化");
                false
            }
        } else {
            false
        };

        // 如果两种协议都没有解析到交易，记录调试信息
        if !pump_has_trades && !bonk_has_trades {
            debug!("分发器未能解析到任何已知协议的交易数据 (payload长度: {})", payload.len());
        }
    }
}