/// 交易分发器模块
/// 
/// 负责订阅Redis，解析交易，然后分发给对应的协议处理器

use anyhow::{anyhow, Result};
use futures_util::stream::StreamExt;
use tracing::{info, debug, error, warn};
use std::sync::Arc;
use tokio::sync::mpsc;

use crate::protocols::pump::parse_trades_from_redis_bytes;
use crate::protocols::bonk::core::{parse_bonk_data_from_redis, BonkProcessor};
use crate::shared::types::{HotPathTrade, WalletConfig, FollowMode, AutoSuspendConfig};
use crate::protocols::pump::TransactionBuilder;
use solana_sdk::pubkey::Pubkey;

const TRADES_CHANNEL: &str = "trades_channel";

/// 交易分发器 - 订阅Redis并分发
pub struct TradeDispatcher {
    redis_url: String,
    user_wallet_pubkey: Pubkey,
    /// 发送给Pump处理器的通道
    pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
    /// Bonk处理器实例（直接调用）
    bonk_processor: Option<Arc<BonkProcessor>>,
}

impl TradeDispatcher {
    /// 创建新的交易分发器
    pub fn new(
        redis_url: &str,
        transaction_builder: Arc<TransactionBuilder>,
        pump_tx: mpsc::UnboundedSender<Vec<HotPathTrade>>,
        bonk_processor: Option<Arc<BonkProcessor>>,
    ) -> Self {
        let user_wallet_pubkey = transaction_builder.get_wallet_pubkey();

        Self {
            redis_url: redis_url.to_string(),
            user_wallet_pubkey,
            pump_tx,
            bonk_processor,
        }
    }
    
    /// 启动分发器 - 统一订阅Redis并智能分发
    pub async fn start(&self) -> Result<()> {
        let client = redis::Client::open(self.redis_url.as_ref())
            .map_err(|e| anyhow!("创建Redis客户端失败: {}", e))?;

        let mut pubsub_conn = client
            .get_async_pubsub()
            .await
            .map_err(|e| anyhow!("获取PubSub连接失败: {}", e))?;

        pubsub_conn
            .subscribe(TRADES_CHANNEL)
            .await
            .map_err(|e| anyhow!("订阅频道 '{}' 失败: {}", TRADES_CHANNEL, e))?;

        info!("交易分发器已成功订阅Redis频道: {}", TRADES_CHANNEL);

        let mut msg_stream = pubsub_conn.on_message();
        
        while let Some(msg) = msg_stream.next().await {
            let payload: Vec<u8> = msg.get_payload_bytes().to_vec();
            
            // 智能分发：同时尝试解析Pump和Bonk协议
            self.dispatch_trades(&payload).await;
        }
        
        warn!("交易分发器消息流已结束");
        Ok(())
    }
    
    /// 智能分发交易数据到对应的协议处理器
    async fn dispatch_trades(&self, payload: &[u8]) {
        // 尝试解析为Pump协议交易
        let pump_trades = parse_trades_from_redis_bytes(payload, &self.user_wallet_pubkey);
        let pump_count = pump_trades.len();
        let pump_has_trades = !pump_trades.is_empty();
        
        if pump_has_trades {
            debug!("分发器解析到 {} 笔Pump交易", pump_count);
            if let Err(e) = self.pump_tx.send(pump_trades) {
                error!("发送交易到Pump处理器失败: {}", e);
            }
        }
        
        // 尝试解析为Bonk协议交易
        let bonk_has_trades = if parse_bonk_data_from_redis(payload).is_some() {
            // 直接调用Bonk处理器进行真实处理
            if let Some(bonk_processor) = &self.bonk_processor {
                // 创建临时钱包配置
                let temp_config = WalletConfig {
                    wallet_address: "temp".to_string(),
                    is_active: true,
                    remark: None,
                    follow_mode: FollowMode::FixedAmount,
                    follow_percentage: None,
                    fixed_follow_amount_sol: Some(0.001),
                    sol_amount_min: Some(0.0001),
                    sol_amount_max: Some(1.0),
                    min_price_multiplier: Some(0.0),
                    max_price_multiplier: Some(1.0),
                    slippage_percentage: 5.0,
                    priority_fee: 150000,
                    compute_unit_limit: 80000,
                    take_profit_start_pct: Some(5.0),
                    take_profit_step_pct: Some(10.0),
                    take_profit_sell_portion_pct: Some(100.0),
                    take_profit_strategy: None,
                    trailing_stop_profit_percentage: None,
                    exponential_sell_trigger_step_pct: None,
                    exponential_sell_base_portion_pct: None,
                    exponential_sell_power: None,
                    volatility_bb_window_size: None,
                    volatility_bb_stddev: None,
                    volatility_atr_samples: None,
                    volatility_atr_multiplier: None,
                    volatility_sell_percent: None,
                    volatility_cooldown_ms: None,
                    stop_loss_percentage: Some(10.0),
                    entry_confirmation_ms: Some(500),
                    dynamic_hold_trigger_pct: Some(0.5),
                    dynamic_hold_check_window_secs: Some(2),
                    dynamic_hold_extend_ms: Some(500),
                    dynamic_hold_max_ms: Some(5000),
                    hard_stop_loss_pct: Some(10.0),
                    callback_stop_pct: Some(10.0),
                    accelerator_tip_percentage: Some(1.0),
                    take_profit_percentage_legacy: None,
                    auto_suspend_config: Some(AutoSuspendConfig {
                        window_size: 1,
                        loss_count: 1,
                        loss_threshold: -5.0,
                    }),
                    min_partial_sell_pct: Some(0.5),
                    sell_slippage_percentage: Some(5.0),
                    sell_priority_fee: Some(150000),
                    sell_tip_percentage: Some(1.0),
                };

                match bonk_processor.process_redis_message(payload, &temp_config).await {
                    Ok(_) => true,
                    Err(e) => {
                        error!("❌ Bonk交易处理失败: {}", e);
                        false
                    }
                }
            } else {
                warn!("⚠️ Bonk处理器未初始化");
                false
            }
        } else {
            false
        };

        // 如果两种协议都没有解析到交易，记录调试信息
        if !pump_has_trades && !bonk_has_trades {
            debug!("分发器未能解析到任何已知协议的交易数据 (payload长度: {})", payload.len());
        }
    }
}