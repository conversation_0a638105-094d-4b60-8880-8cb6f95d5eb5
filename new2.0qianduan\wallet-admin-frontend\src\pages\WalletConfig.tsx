import React, { useState, useMemo, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Form,
  message,
  Modal,
  Input,
  Tabs,
  Badge,
} from 'antd';
import {
  ReloadOutlined,
  WalletOutlined,
  ImportOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import ApiService from '../services/api';
import type { WalletConfig, WalletConfigsResponse } from '../types';
import { priceMultiplierToUsd } from '../utils/priceUtils';
import { 
  WalletConfigTable, 
  WalletFormModal 
} from '../components/WalletConfig';
import TemplateModal from '../components/WalletConfig/TemplateModal';
import BatchImportModal from '../components/WalletConfig/BatchImportModal';
import { useWalletTemplates } from '../hooks/useWalletTemplates';
import { useWalletRemarks } from '../hooks/useWalletRemarks';
import { WalletTemplate, BatchImportWallet, BatchImportProgress } from '../types/template';

const { Title } = Typography;
const { TabPane } = Tabs;

// 协议类型定义
type ProtocolType = 'pump' | 'bonk';

// 钱包配置管理页面 - 支持多协议切换
const WalletConfigPage: React.FC = React.memo(() => {
  const [activeProtocol, setActiveProtocol] = useState<ProtocolType>('pump');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [batchImportModalVisible, setBatchImportModalVisible] = useState(false);
  const [saveTemplateModalVisible, setSaveTemplateModalVisible] = useState(false);
  const [editingWallet, setEditingWallet] = useState<WalletConfig | null>(null);
  const [savingTemplateWallet, setSavingTemplateWallet] = useState<WalletConfig | null>(null);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [saveTemplateForm] = Form.useForm();
  const queryClient = useQueryClient();

  // 获取缓存的SOL价格，避免重复API请求（MainLayout已经在请求了）
  const solPrice = ApiService.getCachedSolPrice();

  // 本地钱包备注管理
  const { setWalletRemark, loadRemarks, updateServerRemarks } = useWalletRemarks();

  // 模板管理
  const { createTemplateFromConfig } = useWalletTemplates();

  // 根据协议类型获取钱包配置的函数
  const getWalletConfigsFunction = useCallback(() => {
    return activeProtocol === 'pump' 
      ? ApiService.getWalletConfigurations()
      : ApiService.getBonkWalletConfigurations();
  }, [activeProtocol]);

  // 根据协议类型更新钱包配置的函数
  const updateWalletConfigFunction = useCallback((config: WalletConfig) => {
    return activeProtocol === 'pump'
      ? ApiService.updateWalletConfiguration(config)
      : ApiService.updateBonkWalletConfiguration(config);
  }, [activeProtocol]);

  // 根据协议类型删除钱包配置的函数  
  const deleteWalletConfigFunction = useCallback((address: string) => {
    return activeProtocol === 'pump'
      ? ApiService.deleteWalletConfiguration(address)
      : ApiService.deleteBonkWalletConfiguration(address);
  }, [activeProtocol]);

  // 获取钱包配置数据
  const { data: walletConfigs, isLoading, refetch } = useQuery<WalletConfigsResponse>({
    queryKey: ['walletConfigs', activeProtocol],
    queryFn: getWalletConfigsFunction,
  });

  // 优化：缓存表格数据转换
  const tableData = useMemo(() => {
    if (!walletConfigs) return [];
    
    return Object.values(walletConfigs).map(config => ({
      ...config,
      key: config.wallet_address,
    }));
  }, [walletConfigs]);

  // 同步服务器端备注到本地状态
  React.useEffect(() => {
    if (walletConfigs) {
      updateServerRemarks(walletConfigs);
    }
  }, [walletConfigs, updateServerRemarks]);

  // 更新钱包配置
  const updateMutation = useMutation({
    mutationFn: updateWalletConfigFunction,
    onSuccess: () => {
      const isAddOperation = !editingWallet;
      const protocolName = activeProtocol === 'pump' ? 'Pump' : 'Bonk';

      message.success(isAddOperation ? `${protocolName}钱包配置添加成功` : `${protocolName}钱包配置更新成功`);
      queryClient.invalidateQueries({ queryKey: ['walletConfigs', activeProtocol] });

      setEditModalVisible(false);
      setAddModalVisible(false);
      setEditingWallet(null);
      form.resetFields();
      addForm.resetFields();
    },
    onError: (error) => {
      message.error(`操作失败: ${error.message}`);
    },
  });

  // 删除钱包配置
  const deleteMutation = useMutation({
    mutationFn: deleteWalletConfigFunction,
    onSuccess: () => {
      const protocolName = activeProtocol === 'pump' ? 'Pump' : 'Bonk';
      message.success(`${protocolName}钱包配置删除成功`);
      queryClient.invalidateQueries({ queryKey: ['walletConfigs', activeProtocol] });
    },
    onError: (error) => {
      message.error(`删除失败: ${error.message}`);
    },
  });

  // 批量删除钱包配置
  const batchDeleteMutation = useMutation({
    mutationFn: async (walletAddresses: string[]) => {
      const BATCH_SIZE = 5;
      const results = [];
      
      for (let i = 0; i < walletAddresses.length; i += BATCH_SIZE) {
        const batch = walletAddresses.slice(i, i + BATCH_SIZE);
        const batchPromises = batch.map(address => 
          deleteWalletConfigFunction(address)
        );
        
        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults);
      }
      
      return results;
    },
    onMutate: async (walletAddresses: string[]) => {
      await queryClient.cancelQueries({ queryKey: ['walletConfigs', activeProtocol] });
      
      const previousData = queryClient.getQueryData<WalletConfigsResponse>(['walletConfigs', activeProtocol]);
      
      if (previousData) {
        const optimisticData = { ...previousData };
        walletAddresses.forEach(address => {
          delete optimisticData[address];
        });
        
        queryClient.setQueryData(['walletConfigs', activeProtocol], optimisticData);
      }
      
      return { previousData };
    },
    onSuccess: (results) => {
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failedCount = results.filter(r => r.status === 'rejected').length;
      const protocolName = activeProtocol === 'pump' ? 'Pump' : 'Bonk';
      
      if (failedCount === 0) {
        message.success(`成功删除 ${successCount} 个${protocolName}钱包配置`);
      } else {
        message.warning(`${protocolName}删除完成：成功 ${successCount} 个，失败 ${failedCount} 个`);
      }
      
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['walletConfigs', activeProtocol] });
      }, 300);
    },
    onError: (error, _, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(['walletConfigs', activeProtocol], context.previousData);
      }
      message.error(`批量删除失败: ${error.message}`);
    },
  });

  // 批量状态切换
  const batchStatusToggleMutation = useMutation({
    mutationFn: async ({ walletAddresses, isActive }: { walletAddresses: string[], isActive: boolean }) => {
      const currentConfigs = walletConfigs ? Object.values(walletConfigs) : [];
      const BATCH_SIZE = 5;
      const results = [];
      
      for (let i = 0; i < walletAddresses.length; i += BATCH_SIZE) {
        const batch = walletAddresses.slice(i, i + BATCH_SIZE);
        const batchPromises = batch.map(address => {
          const currentConfig = currentConfigs.find(config => config.wallet_address === address);
          if (currentConfig) {
            const updatedConfig = { ...currentConfig, is_active: isActive };
            return updateWalletConfigFunction(updatedConfig);
          }
          return Promise.reject(new Error(`找不到钱包配置: ${address}`));
        });
        
        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults);
      }
      
      return results;
    },
    onMutate: async ({ walletAddresses, isActive }) => {
      await queryClient.cancelQueries({ queryKey: ['walletConfigs', activeProtocol] });
      
      const previousData = queryClient.getQueryData<WalletConfigsResponse>(['walletConfigs', activeProtocol]);
      
      if (previousData) {
        const optimisticData = { ...previousData };
        walletAddresses.forEach(address => {
          if (optimisticData[address]) {
            optimisticData[address] = {
              ...optimisticData[address],
              is_active: isActive
            };
          }
        });
        
        queryClient.setQueryData(['walletConfigs', activeProtocol], optimisticData);
      }
      
      return { previousData };
    },
    onSuccess: (results, { isActive }) => {
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failedCount = results.filter(r => r.status === 'rejected').length;
      const protocolName = activeProtocol === 'pump' ? 'Pump' : 'Bonk';
      const action = isActive ? '启用' : '停用';
      
      if (failedCount === 0) {
        message.success(`成功${action} ${successCount} 个${protocolName}钱包配置`);
      } else {
        message.warning(`${protocolName} ${action}完成：成功 ${successCount} 个，失败 ${failedCount} 个`);
      }
      
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['walletConfigs', activeProtocol] });
      }, 300);
    },
    onError: (error, _, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(['walletConfigs', activeProtocol], context.previousData);
      }
      message.error(`批量状态切换失败: ${error.message}`);
    },
  });

  // 事件处理函数 (保持原有逻辑，但使用动态的函数)
  const handleStatusToggle = useCallback(async (wallet: WalletConfig, checked: boolean) => {
    const updatedConfig = { ...wallet, is_active: checked };
    updateMutation.mutate(updatedConfig);
  }, [updateMutation]);

  const handleEdit = useCallback((wallet: WalletConfig) => {
    setEditingWallet(wallet);

    const formValues = {
      ...wallet,
      min_price_usd: wallet.min_price_multiplier ? priceMultiplierToUsd(wallet.min_price_multiplier, solPrice) : undefined,
      max_price_usd: wallet.max_price_multiplier ? priceMultiplierToUsd(wallet.max_price_multiplier, solPrice) : undefined,
    };

    form.setFieldsValue(formValues);
    setEditModalVisible(true);
  }, [form, solPrice]);

  const handleDelete = useCallback((walletAddress: string) => {
    deleteMutation.mutate(walletAddress);
  }, [deleteMutation]);

  const handleBatchDelete = useCallback((walletAddresses: string[]) => {
    batchDeleteMutation.mutate(walletAddresses);
  }, [batchDeleteMutation]);

  const handleBatchStatusToggle = useCallback((walletAddresses: string[], isActive: boolean) => {
    batchStatusToggleMutation.mutate({ walletAddresses, isActive });
  }, [batchStatusToggleMutation]);

  const handleBatchImport = useCallback(async (
    wallets: BatchImportWallet[], 
    template: WalletTemplate,
    onProgress: (progress: BatchImportProgress) => void
  ) => {
    const errors: string[] = [];
    let successCount = 0;
    let failedCount = 0;
    
    onProgress({
      total: wallets.length,
      current: 0,
      success: 0,
      failed: 0,
      errors: []
    });
    
    for (let i = 0; i < wallets.length; i++) {
      const wallet = wallets[i];
      try {
        const newConfig: WalletConfig = {
          ...template.config,
          wallet_address: wallet.wallet_address,
          remark: wallet.remark,
        };
        
        await updateWalletConfigFunction(newConfig);
        successCount++;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        const errorMsg = `${wallet.wallet_address}: ${errorMessage}`;
        errors.push(errorMsg);
        failedCount++;
      }
      
      onProgress({
        total: wallets.length,
        current: i + 1,
        success: successCount,
        failed: failedCount,
        errors: errors
      });
    }
    
    queryClient.invalidateQueries({ queryKey: ['walletConfigs', activeProtocol] });
    
    setTimeout(() => {
      loadRemarks();
    }, 100);
    
    const protocolName = activeProtocol === 'pump' ? 'Pump' : 'Bonk';
    if (errors.length === 0) {
      message.success(`成功导入 ${successCount} 个${protocolName}钱包配置`);
    } else {
      message.warning(`${protocolName}导入完成，成功: ${successCount}，失败: ${errors.length}`);
    }
  }, [queryClient, setWalletRemark, loadRemarks, activeProtocol, updateWalletConfigFunction]);

  const handleSaveAsTemplate = useCallback((wallet: WalletConfig) => {
    setSavingTemplateWallet(wallet);
    setSaveTemplateModalVisible(true);
  }, []);

  const handleSaveTemplateSubmit = useCallback(async () => {
    if (!savingTemplateWallet) return;
    
    try {
      const values = await saveTemplateForm.validateFields();
      createTemplateFromConfig(
        values.templateName,
        savingTemplateWallet,
        values.templateDescription
      );
      message.success('模板保存成功');
      setSaveTemplateModalVisible(false);
      setSavingTemplateWallet(null);
      saveTemplateForm.resetFields();
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error('模板保存失败');
    }
  }, [savingTemplateWallet, saveTemplateForm, createTemplateFromConfig]);

  const handleSaveTemplateCancel = useCallback(() => {
    setSaveTemplateModalVisible(false);
    setSavingTemplateWallet(null);
    saveTemplateForm.resetFields();
  }, [saveTemplateForm]);

  const handleTemplateSelect = useCallback((template: WalletTemplate) => {
    const templateConfig = {
      ...template.config,
      wallet_address: '',
      remark: '',
    };
    
    addForm.setFieldsValue(templateConfig);
    setAddModalVisible(true);
  }, [addForm]);

  const handleEditSubmit = useCallback(async (values: any) => {
    if (!editingWallet) return;

    const updatedConfig: WalletConfig = {
      ...editingWallet,
      ...values,
    };

    updateMutation.mutate(updatedConfig);
  }, [editingWallet, updateMutation]);

  const handleAddSubmit = useCallback(async (values: any) => {
    const newConfig: WalletConfig = {
      wallet_address: values.wallet_address,
      is_active: values.is_active ?? true,
      follow_mode: values.follow_mode || 'Percentage',
      slippage_percentage: values.slippage_percentage,
      priority_fee: values.priority_fee,
      compute_unit_limit: values.compute_unit_limit,
      ...values,
    };

    updateMutation.mutate(newConfig);
  }, [updateMutation]);

  const handleEditCancel = useCallback(() => {
    setEditModalVisible(false);
    setEditingWallet(null);
    form.resetFields();
  }, [form]);

  const handleAddCancel = useCallback(() => {
    setAddModalVisible(false);
    addForm.resetFields();
  }, [addForm]);

  // 获取活跃钱包数量用于徽章显示
  const activeWalletCount = useMemo(() => {
    if (!walletConfigs) return 0;
    return Object.values(walletConfigs).filter(w => w.is_active).length;
  }, [walletConfigs]);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={2}>
          <WalletOutlined /> 多协议钱包配置管理
        </Title>
        <Space>
          <Button
            type="primary"
            onClick={() => setAddModalVisible(true)}
          >
            添加钱包
          </Button>
          <Button
            icon={<ImportOutlined />}
            onClick={() => setBatchImportModalVisible(true)}
          >
            批量导入
          </Button>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setTemplateModalVisible(true)}
          >
            模板管理
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Card>
        <Tabs 
          activeKey={activeProtocol} 
          onChange={(key) => setActiveProtocol(key as ProtocolType)}
          items={[
            {
              key: 'pump',
              label: (
                <Badge count={activeProtocol === 'pump' ? activeWalletCount : 0} showZero={false}>
                  <Space>
                    <ThunderboltOutlined />
                    Pump协议
                  </Space>
                </Badge>
              ),
              children: (
                <WalletConfigTable
                  data={tableData}
                  loading={isLoading}
                  solPrice={solPrice}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onBatchDelete={handleBatchDelete}
                  onBatchStatusToggle={handleBatchStatusToggle}
                  onSaveAsTemplate={handleSaveAsTemplate}
                  onStatusToggle={handleStatusToggle}
                  updateLoading={updateMutation.isPending || batchStatusToggleMutation.isPending}
                  deleteLoading={deleteMutation.isPending || batchDeleteMutation.isPending}
                />
              ),
            },
            {
              key: 'bonk',
              label: (
                <Badge count={activeProtocol === 'bonk' ? activeWalletCount : 0} showZero={false}>
                  <Space>
                    <RocketOutlined />
                    Bonk协议
                  </Space>
                </Badge>
              ),
              children: (
                <WalletConfigTable
                  data={tableData}
                  loading={isLoading}
                  solPrice={solPrice}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onBatchDelete={handleBatchDelete}
                  onBatchStatusToggle={handleBatchStatusToggle}
                  onSaveAsTemplate={handleSaveAsTemplate}
                  onStatusToggle={handleStatusToggle}
                  updateLoading={updateMutation.isPending || batchStatusToggleMutation.isPending}
                  deleteLoading={deleteMutation.isPending || batchDeleteMutation.isPending}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* 编辑模态框 */}
      {editModalVisible && (
        <WalletFormModal
          visible={editModalVisible}
          mode="edit"
          editingWallet={editingWallet}
          solPrice={solPrice}
          form={form}
          onSubmit={handleEditSubmit}
          onCancel={handleEditCancel}
          loading={updateMutation.isPending}
          protocolType={activeProtocol}
        />
      )}

      {/* 添加模态框 */}
      {addModalVisible && (
        <WalletFormModal
          visible={addModalVisible}
          mode="add"
          solPrice={solPrice}
          form={addForm}
          onSubmit={handleAddSubmit}
          onCancel={handleAddCancel}
          loading={updateMutation.isPending}
          protocolType={activeProtocol}
        />
      )}

      {/* 保存模板模态框 */}
      {saveTemplateModalVisible && (
        <Modal
          title={`保存为${activeProtocol === 'pump' ? 'Pump' : 'Bonk'}模板`}
          open={saveTemplateModalVisible}
          onOk={handleSaveTemplateSubmit}
          onCancel={handleSaveTemplateCancel}
          width={500}
        >
          <div style={{ marginBottom: 16 }}>
            <p>将当前{activeProtocol === 'pump' ? 'Pump' : 'Bonk'}配置保存为模板，以便批量应用到其他钱包。</p>
          </div>
          <Form form={saveTemplateForm} layout="vertical">
            <Form.Item 
              name="templateName" 
              label="模板名称"
              rules={[{ required: true, message: '请输入模板名称' }]}
            >
              <Input placeholder={`例如：${activeProtocol === 'pump' ? 'Pump' : 'Bonk'}高频交易模板`} />
            </Form.Item>
            <Form.Item name="templateDescription" label="模板描述">
              <Input.TextArea 
                rows={3} 
                placeholder={`描述该${activeProtocol === 'pump' ? 'Pump' : 'Bonk'}模板的使用场景和特点...`} 
              />
            </Form.Item>
          </Form>
        </Modal>
      )}

      {/* 模板管理模态框 */}
      {templateModalVisible && (
        <TemplateModal
          visible={templateModalVisible}
          onCancel={() => setTemplateModalVisible(false)}
          onSelectTemplate={handleTemplateSelect}
          protocolType={activeProtocol}
        />
      )}

      {/* 批量导入模态框 */}
      {batchImportModalVisible && (
        <BatchImportModal
          visible={batchImportModalVisible}
          onCancel={() => setBatchImportModalVisible(false)}
          onImport={handleBatchImport}
          loading={updateMutation.isPending}
          protocolType={activeProtocol}
        />
      )}
    </div>
  );
});

WalletConfigPage.displayName = 'WalletConfigPage';

export default WalletConfigPage;