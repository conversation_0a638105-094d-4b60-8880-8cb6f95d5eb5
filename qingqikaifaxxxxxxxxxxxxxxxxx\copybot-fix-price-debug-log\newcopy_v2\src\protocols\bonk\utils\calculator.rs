use crate::shared::types::{WalletConfig, TipProvider};
use colored::Colorize;
use tracing::{info, warn};
use std::time::Duration;

/// Bonk计算结果，复用pump的结构
#[derive(Debug, Clone)]
pub struct BonkCalculationResult {
    pub should_trade: bool,
    pub reason: &'static str,
    pub final_buy_amount_sol: f64,
    pub min_expected_token_amount_out: u64,
    pub priority_fee: u64,
    pub compute_unit_limit: u32,
    pub tip_details: Option<BonkTipDetails>,
}

/// Bonk小费详情
#[derive(Debug, Clone)]
pub struct BonkTipDetails {
    pub provider: TipProvider,
    pub amount_lamports: u64,
}

/// Bonk计算输入参数
pub struct BonkCalculateInput<'a> {
    pub buy_amount_sol: f64,
    pub trade_price: f64,
    pub config: &'a WalletConfig,
}

impl BonkCalculationResult {
    /// 创建一个不交易的返回结果
    pub fn do_not_trade(reason: &'static str) -> Self {
        Self {
            should_trade: false,
            reason,
            final_buy_amount_sol: 0.0,
            min_expected_token_amount_out: 0,
            priority_fee: 0,
            compute_unit_limit: 0,
            tip_details: None,
        }
    }
}

/// Bonk高性能计算器，复用pump的逻辑
#[derive(Debug, Clone, Default)]
pub struct BonkCalculator;

impl BonkCalculator {
    pub fn new() -> Self {
        Self::default()
    }

    /// 核心热路径计算函数，复用pump的逻辑
    #[inline(always)]
    pub fn calculate(&self, input: BonkCalculateInput) -> BonkCalculationResult {
        // 计算目标代币数量
        let target_tokens = input.buy_amount_sol / input.trade_price;
        let target_token_amount_out = target_tokens as u64;

        if target_token_amount_out == 0 {
            return BonkCalculationResult {
                should_trade: false,
                reason: "目标代币数量为0",
                final_buy_amount_sol: 0.0,
                min_expected_token_amount_out: 0,
                priority_fee: 0,
                compute_unit_limit: 0,
                tip_details: None,
            };
        }

        // 计算最大SOL成本（加上滑点保护）
        let base_sol_cost = input.buy_amount_sol;
        let max_sol_cost = base_sol_cost * (1.0 + input.config.slippage_percentage / 100.0);

        // 构建并返回成功的计算结果
        BonkCalculationResult {
            should_trade: true,
            reason: "Bonk计算成功",
            final_buy_amount_sol: max_sol_cost,
            min_expected_token_amount_out: target_token_amount_out,
            priority_fee: input.config.priority_fee,
            compute_unit_limit: input.config.compute_unit_limit,
            tip_details: None,
        }
    }
}

/// 冷路径处理函数 - Bonk专用
fn format_duration_as_us(d: Duration) -> String {
    format!("{:.1}µs", d.as_nanos() as f64 / 1000.0)
}

/// Bonk计算结果日志记录
pub fn log_bonk_calculation_result(
    result: &BonkCalculationResult,
    total_duration: Duration,
    filter_duration: Duration,
    calculation_duration: Duration,
    build_duration: Duration,
    build_to_send_delay: Duration,
) {
    let details = format!(
        "Bonk计算结果: 购买 {}, 最低接收 {} | 延迟: {} (筛选: {}, 计算: {}, 构建: {}, 构建→发送: {})",
        result.final_buy_amount_sol.to_string().green(),
        result.min_expected_token_amount_out.to_string().green(),
        format_duration_as_us(total_duration).red(),
        format_duration_as_us(filter_duration).yellow(),
        format_duration_as_us(calculation_duration).yellow(),
        format_duration_as_us(build_duration).yellow(),
        format_duration_as_us(build_to_send_delay).yellow()
    );

    if total_duration.as_micros() > 200 {
        warn!("{}", details);
    } else {
        info!("{}", details);
    }
}