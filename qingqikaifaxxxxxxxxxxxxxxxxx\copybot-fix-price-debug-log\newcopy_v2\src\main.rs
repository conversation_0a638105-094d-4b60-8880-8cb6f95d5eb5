use anyhow::Result;
use crate::{
    protocols::pump::{
        Filter, FilterConfig, FilterCommand,
        RedisSubscriber,
        TransactionBuilder,
    },
    protocols::bonk::{
        <PERSON>k<PERSON><PERSON><PERSON>, BonkFilterConfig, Bonk<PERSON><PERSON>erCommand,
        BonkTransactionBuilder,
    },
    blockhash_service::BlockhashService,
    config::Settings,
};
use std::sync::Arc;

use tracing::{debug, error, info, warn};
use crate::logging::{init_logging, is_production_mode};
use arc_swap::ArcSwap;
use futures_util::stream::StreamExt;
use redis::AsyncCommands;
use solana_sdk::signature::{Keypair, Signer};
use bs58;
use std::time::Duration;
use services::transaction_sender::TransactionSender;
use services::ata_cache::AtaCache;
use services::api_server::start_api_server;
use crate::shared::constants::{FILTER_CONFIG_KEY, FILTER_UPDATE_CHANNEL, BONK_FILTER_CONFIG_KEY, BONK_FILTER_UPDATE_CHANNEL};
use crate::services::trade_logger::TradeLogger;
use crate::services::price_sse_manager::PriceSseManager;
use crate::services::sol_price_oracle::SolPriceOracle;
use crate::services::trade_event_manager::TradeEventManager;
use crate::services::transaction_tracker::{TransactionTracker, TrackRequest};
use tokio::sync::mpsc;

// --- 引入新模块 ---
use crate::services::trade_dispatcher::TradeDispatcher;
use crate::services::price_broadcast::PriceBroadcastManager;
use crate::services::sell_executor::SellExecutor;
use crate::services::auto_suspend_manager::AutoSuspendManager;
use dashmap::DashMap;
use solana_sdk::pubkey::Pubkey;

pub mod hotpath;
mod shared;
pub mod blockhash_service;
mod config;
mod services;
mod strategies;
mod logging;
mod protocols;

/// 监听配置更新的后台任务
async fn listen_for_filter_updates(
    redis_client: redis::Client,
    filter_container: Arc<ArcSwap<Filter>>,
) -> Result<()> {
    info!("启动筛选器配置更新监听器，订阅频道: {}", FILTER_UPDATE_CHANNEL);
    
    let mut pubsub_conn = redis_client.get_async_pubsub().await?;
    pubsub_conn.subscribe(FILTER_UPDATE_CHANNEL).await?;
    let mut msg_stream = pubsub_conn.on_message();

    let mut cmd_conn = redis_client.get_multiplexed_async_connection().await?;

    while let Some(msg) = msg_stream.next().await {
        let payload = msg.get_payload_bytes();
        if let Ok(command) = serde_json::from_slice::<FilterCommand>(payload) {
            // 简化显示筛选命令，不显示详细的结构体内容
            match &command {
                FilterCommand::UpdateWallet(wallet_config) => {
                    info!("正在应用钱包配置更新: {}", wallet_config.wallet_address);
                },
                FilterCommand::RemoveWallet(address) => {
                    info!("正在移除钱包配置: {}", address);
                },
                FilterCommand::AddCreator(creator) => {
                    info!("正在添加创建者: {}", creator);
                },
                FilterCommand::RemoveCreator(creator) => {
                    info!("正在移除创建者: {}", creator);
                },
                FilterCommand::SetMinPrice(price) => {
                    info!("正在设置最低价格: {}", price);
                },
                FilterCommand::SetMaxPrice(price) => {
                    info!("正在设置最高价格: {}", price);
                },
                _ => {
                    info!("正在应用筛选器配置更新");
                }
            }
            
            let mut retries = 5;
            while retries > 0 {
                let _: () = redis::cmd("WATCH").arg(FILTER_CONFIG_KEY).query_async(&mut cmd_conn).await?;
                
                let config_json: Option<String> = cmd_conn.get(FILTER_CONFIG_KEY).await?;
                
                let current_config: FilterConfig = config_json
                    .and_then(|json| serde_json::from_str(&json).ok())
                    .unwrap_or_default();

                let new_config = current_config.apply_command(command.clone());
                let new_config_json = serde_json::to_string(&new_config)?;

                let result: Option<()> = redis::pipe()
                    .atomic()
                    .set(FILTER_CONFIG_KEY, &new_config_json)
                    .query_async(&mut cmd_conn)
                    .await?;

                if result.is_some() {
                    filter_container.store(Arc::new(Filter::new(new_config)));
                    info!("筛选器配置已原子化更新并持久化。");
                    break; 
                } else {
                    warn!("配置键被并发修改，正在重试更新... (剩余次数: {})", retries - 1);
                    retries -= 1;
                    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
                }
            }
            if retries == 0 {
                error!("更新配置失败，已达到最大重试次数。");
            }
        } else {
            error!("反序列化筛选器命令失败. Payload: {}", String::from_utf8_lossy(payload));
        }
    }
    warn!("筛选器更新监听器已停止。");
    Ok(())
}

/// 监听Bonk配置更新的后台任务
async fn listen_for_bonk_filter_updates(
    redis_client: redis::Client,
    filter_container: Arc<ArcSwap<BonkFilter>>,
) -> Result<()> {
    info!("启动Bonk筛选器配置更新监听器，订阅频道: {}", BONK_FILTER_UPDATE_CHANNEL);
    
    let mut pubsub_conn = redis_client.get_async_pubsub().await?;
    pubsub_conn.subscribe(BONK_FILTER_UPDATE_CHANNEL).await?;
    let mut msg_stream = pubsub_conn.on_message();

    let mut cmd_conn = redis_client.get_multiplexed_async_connection().await?;

    while let Some(msg) = msg_stream.next().await {
        let payload = msg.get_payload_bytes();
        if let Ok(command) = serde_json::from_slice::<BonkFilterCommand>(payload) {
            match &command {
                BonkFilterCommand::UpdateWallet(wallet_config) => {
                    info!("正在应用Bonk钱包配置更新: {}", wallet_config.wallet_address);
                },
                BonkFilterCommand::RemoveWallet(address) => {
                    info!("正在移除Bonk钱包配置: {}", address);
                },
                BonkFilterCommand::AddCreator(creator) => {
                    info!("正在添加Bonk创建者: {}", creator);
                },
                BonkFilterCommand::RemoveCreator(creator) => {
                    info!("正在移除Bonk创建者: {}", creator);
                },
                BonkFilterCommand::SetMinPrice(price) => {
                    info!("正在设置Bonk最低价格: {}", price);
                },
                BonkFilterCommand::SetMaxPrice(price) => {
                    info!("正在设置Bonk最高价格: {}", price);
                },
                _ => {
                    info!("正在应用Bonk筛选器配置更新");
                }
            }
            
            let mut retries = 5;
            while retries > 0 {
                let _: () = redis::cmd("WATCH").arg(BONK_FILTER_CONFIG_KEY).query_async(&mut cmd_conn).await?;
                
                let config_json: Option<String> = cmd_conn.get(BONK_FILTER_CONFIG_KEY).await?;
                
                let current_config: BonkFilterConfig = config_json
                    .and_then(|json| serde_json::from_str(&json).ok())
                    .unwrap_or_default();

                let new_config = current_config.apply_command(command.clone());
                let new_config_json = serde_json::to_string(&new_config)?;

                let result: Option<()> = redis::pipe()
                    .atomic()
                    .set(BONK_FILTER_CONFIG_KEY, &new_config_json)
                    .query_async(&mut cmd_conn)
                    .await?;

                if result.is_some() {
                    filter_container.store(Arc::new(BonkFilter::new(new_config)));
                    info!("Bonk筛选器配置已原子化更新并持久化。");
                    break; 
                } else {
                    warn!("Bonk配置键被并发修改，正在重试更新... (剩余次数: {})", retries - 1);
                    retries -= 1;
                    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
                }
            }
            if retries == 0 {
                error!("更新Bonk配置失败，已达到最大重试次数。");
            }
        } else {
            error!("反序列化Bonk筛选器命令失败. Payload: {}", String::from_utf8_lossy(payload));
        }
    }
    warn!("Bonk筛选器更新监听器已停止。");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // --- 日志初始化 ---
    let is_production = is_production_mode();
    let log_sse_manager = init_logging(is_production);

    // --- 0. 加载配置 ---
    let settings = Settings::new().expect("加载配置文件 settings.toml 失败");
    
    // --- 0.1. 初始化全局配置 ---
    shared::global_config::initialize_accelerator_config(settings.accelerator.clone());
    info!("全局加速器配置已初始化。启用状态: {}", shared::global_config::get_accelerator_config().enabled);
    
    info!("配置文件加载成功."); // 精简日志

    info!("正在初始化程序...");

    // --- 1. 初始化区块哈希服务 ---
    info!("正在初始化区块哈希服务...");
    let blockhash_service = Arc::new(BlockhashService::new());
    
    // 在后台启动gRPC订阅
    let blockhash_service_clone = blockhash_service.clone();
    let grpc_endpoints = settings.grpc.endpoints.clone(); // 从配置中获取
    tokio::spawn(async move {
        blockhash_service_clone.start(grpc_endpoints).await;
    });
    info!("区块哈希服务已在后台任务中启动。");

    // --- 1.5. 等待区块哈希服务预热 ---
    info!("正在等待区块哈希服务预热，最长等待30秒...");
    let preheat_timeout = Duration::from_secs(30);
    let preheat_result = tokio::time::timeout(preheat_timeout, async {
        loop {
            if blockhash_service.get_latest_blockhash().is_some() {
                info!("✅ 区块哈希服务已成功预热！");
                break;
            }
            tokio::time::sleep(Duration::from_millis(200)).await;
        }
    }).await;

    if preheat_result.is_err() {
        panic!("错误：区块哈希服务在 {} 秒内未能预热成功，程序即将退出。", preheat_timeout.as_secs());
    }

    // --- 2. 初始化Redis和筛选器配置 ---
    let client = redis::Client::open(settings.redis.url.as_str())?; // 从配置中获取
    let mut con = client.get_multiplexed_async_connection().await?;

    let json_str: Option<String> = con.get(FILTER_CONFIG_KEY).await?;
    let initial_config: FilterConfig = match json_str {
        Some(s) => {
            serde_json::from_str(&s).unwrap_or_else(|e| {
                warn!("从Redis解析配置失败: {}, 使用默认配置。", e);
                FilterConfig::default()
            })
        }
        None => {
            info!("在Redis中未找到配置，创建并存储默认配置。");
            let config = FilterConfig::default();
            let _: () = con.set(FILTER_CONFIG_KEY, serde_json::to_string(&config)?).await?;
            config
        }
    };
    info!("筛选器初始配置加载完成，共配置 {} 个钱包", initial_config.wallet_configs.len());
    debug!("筛选器详细配置: {:?}", initial_config);
    
    // --- 2.1. 初始化Bonk筛选器配置 ---
    let bonk_json_str: Option<String> = con.get(BONK_FILTER_CONFIG_KEY).await?;
    let bonk_initial_config: BonkFilterConfig = match bonk_json_str {
        Some(s) => {
            serde_json::from_str(&s).unwrap_or_else(|e| {
                warn!("从Redis解析Bonk配置失败: {}, 使用默认配置。", e);
                BonkFilterConfig::default()
            })
        }
        None => {
            info!("在Redis中未找到Bonk配置，创建并存储默认配置。");
            let config = BonkFilterConfig::default();
            let _: () = con.set(BONK_FILTER_CONFIG_KEY, serde_json::to_string(&config)?).await?;
            config
        }
    };
    info!("Bonk筛选器初始配置加载完成，共配置 {} 个钱包", bonk_initial_config.wallet_configs.len());
    debug!("Bonk筛选器详细配置: {:?}", bonk_initial_config);
    
    // --- 3. 初始化交易构建器和钱包 ---
    // 从bs58编码的私钥字符串加载钱包
    let private_key_str = &settings.wallet.private_key_bs58; // 从配置中获取
    let keypair_bytes = bs58::decode(private_key_str)
        .into_vec()
        .expect("无效的bs58私钥字符串");
    let wallet_keypair = Arc::new(Keypair::from_bytes(&keypair_bytes).expect("无法从字节码创建密钥对"));
    info!("钱包已加载, 公钥: {}", wallet_keypair.pubkey());

    let transaction_builder = Arc::new(TransactionBuilder::new(
        wallet_keypair.clone(),
        blockhash_service.clone(), // 注入BlockhashService
        settings.rpc.blockhash_lag_slots,
        settings.fee_config.clone(), // 传递手续费配置
    ));
    
    // --- 3.5. 初始化交易发送器 ---
    let transaction_sender = Arc::new(TransactionSender::new(&settings.rpc.url, settings.rpc.skip_preflight, settings.rpc.keep_alive_secs)?);

    // --- 3.6. 初始化 ATA 缓存 ---
    let ata_cache = Arc::new(AtaCache::new(settings.ata.cache_ttl_secs));
    
    // --- 3.7. 初始化核心业务服务 ---
    // 价格推送服务 (SSE)
    let price_sse_manager = PriceSseManager::new();
    // 交易事件推送服务 (SSE)
    let trade_event_manager = TradeEventManager::new();
    
    // SOL/USD 价格预言机
    let sol_price_oracle = SolPriceOracle::new();
    sol_price_oracle.start().await; // 启动后台更新任务

    // 自动暂停管理器
    let auto_suspend_manager = Arc::new(AutoSuspendManager::new(client.clone()));

    // --- 3.7.1 创建交易跟踪通道 ---
    let (tracker_tx, tracker_rx) = mpsc::channel::<TrackRequest>(1024);

    // --- 新架构：初始化 PriceBroadcastManager 和 SellExecutor ---
    let price_broadcast_manager = PriceBroadcastManager::new();

    // --- 新增：价格广播桥接任务 ---
    // 这个任务订阅内部价格广播，并将其转发给外部SSE管理器
    let mut price_receiver = price_broadcast_manager.subscribe();
    let sse_price_broadcaster = price_sse_manager.clone();
    tokio::spawn(async move {
        info!("价格广播桥接任务已启动。");
        while let Ok((mint, price)) = price_receiver.recv().await {
            sse_price_broadcaster
                .lock()
                .unwrap()
                .broadcast(&mint.to_string(), price);
        }
        warn!("价格广播桥接任务已停止。");
    });

    // 创建共享的"已撤销Mint"列表，现在是计数器
    let revoked_mints = Arc::new(DashMap::<Pubkey, u64>::new());

    let sell_executor = SellExecutor::new(
        transaction_builder.clone(),
        transaction_sender.clone(),
        tracker_tx.clone(),
        sol_price_oracle.clone(),
    );

    // --- 启动交易跟踪服务 ---
    let transaction_tracker = TransactionTracker::new(
        trade_event_manager.clone(),
        sol_price_oracle.clone(),
        &settings.rpc.url,
        settings.transaction_tracker.confirmation_timeout_ms,
        sell_executor.clone(),
        price_broadcast_manager.clone(),
        revoked_mints.clone(), // 正确注入到 Tracker
        auto_suspend_manager.clone(),
    );

    // 使用外部通道启动
    transaction_tracker.start_with_receiver(tracker_rx);

    // --- 3.8. 初始化交易历史记录器 ---
    let (trade_logger, trade_log_tx) = TradeLogger::new();
    tokio::spawn(trade_logger.start());

    // --- 4. 初始化交易分发器和处理器 ---
    let (pump_tx, pump_rx) = mpsc::unbounded_channel();
    
    // 创建交易分发器，集成Bonk处理器
    let trade_dispatcher = TradeDispatcher::new(
        &settings.redis.url,
        transaction_builder.clone(),
        pump_tx,
        Some(bonk_processor.clone()), // 传入Bonk处理器
    );
    
    // 创建Pump协议处理器
    let redis_subscriber = RedisSubscriber::new(
        &settings.redis.url,
        transaction_builder.clone(),
        transaction_sender.clone(),
        ata_cache.clone(),
        trade_log_tx.clone(),
        transaction_tracker.clone(),
        tracker_tx.clone(),
        price_broadcast_manager.clone(),
    ).await?;

    // 创建Bonk交易构建器
    let bonk_transaction_builder = Arc::new(
        BonkTransactionBuilder::new(
            wallet_keypair.clone(),
            blockhash_service.clone(),
            &settings.rpc.url,
        )?
    );
    
    // 创建Bonk协议处理器（新架构）
    use crate::protocols::bonk::core::BonkProcessor;
    let bonk_processor = Arc::new(BonkProcessor::new(bonk_transaction_builder));
    
    // 创建协议分发器
    use crate::protocols::dispatcher::ProtocolDispatcher;
    let _protocol_dispatcher = Arc::new(ProtocolDispatcher::new(bonk_processor));

    let filter_container = Arc::new(ArcSwap::new(Arc::new(Filter::new(initial_config))));
    let bonk_filter_container = Arc::new(ArcSwap::new(Arc::new(BonkFilter::new(bonk_initial_config))));

    tokio::spawn(listen_for_filter_updates(
        client.clone(),
        filter_container.clone(),
    ));

    tokio::spawn(listen_for_bonk_filter_updates(
        client.clone(),
        bonk_filter_container.clone(),
    ));

    // --- 5. 准备启动核心服务 ---
    // 先为 API 服务器克隆一个 filter_container 的 Arc 副本
    let api_filter_container = Arc::clone(&filter_container);
    let api_bonk_filter_container = Arc::clone(&bonk_filter_container);
    let api_redis_client = client.clone();
    
    // 启动交易分发器（订阅Redis）
    let _dispatcher_filter_container = Arc::clone(&filter_container);
    tokio::spawn(async move {
        if let Err(e) = trade_dispatcher.start().await {
            error!("交易分发器发生严重错误: {}", e);
        }
    });
    
    // 启动Pump协议处理器（接收分发器消息）
    let pump_filter_container = Arc::clone(&filter_container);
    tokio::spawn(async move {
        if let Err(e) = redis_subscriber.start_processing(pump_filter_container, pump_rx).await {
            error!("Pump协议处理器发生严重错误: {}", e);
        }
    });
    
    // 启动Bonk协议处理器（接收分发器消息）
    // 注意：新架构中不再需要单独的bonk处理任务
    // 协议分发器会自动处理Bonk协议数据
    info!("✅ Bonk协议处理器已集成到协议分发器中");
    
    info!("所有后台服务已启动。");

    // --- 9. 启动API服务器 ---
    // 这是程序的最后一个前台任务。
    // 我们直接 `await` 它，这将阻塞 main 函数，使其不会提前退出。
    // 整个程序将在这里一直运行，直到API服务器因故停止或收到外部的终止信号。
    info!("正在启动API服务器作为前台服务...");
    start_api_server(
        api_filter_container,
        api_bonk_filter_container,
        api_redis_client,
        price_sse_manager, 
        trade_event_manager,
        log_sse_manager,
        settings.auth.clone(),
    ).await;

    info!("API服务器已停止，程序即将退出。");

    Ok(())
}
