# Pump协议模块结构

## 目录结构

```
src/protocols/pump/
├── mod.rs                    # 模块入口，重新导出所有公共类型
├── core/                     # 核心处理逻辑
│   ├── mod.rs               # 核心模块入口
│   ├── processor.rs         # Pump协议处理器 (原redis_subscriber.rs)
│   ├── parser.rs            # 交易解析器
│   └── transaction_builder.rs # 交易构建器
└── utils/                   # 工具模块
    ├── mod.rs               # 工具模块入口
    ├── calculator.rs        # 计算工具
    └── filter.rs            # 筛选工具
```

## 模块功能

### 核心模块 (core/)
- **processor.rs**: Pump协议的主要处理器，负责处理来自分发器的交易
- **parser.rs**: 解析Redis中的交易数据为HotPathTrade结构
- **transaction_builder.rs**: 构建Pump协议的交易

### 工具模块 (utils/)
- **calculator.rs**: 计算跟单金额、滑点等逻辑
- **filter.rs**: 筛选交易的逻辑

## 引用方式

在其他模块中使用Pump协议组件：

```rust
use crate::protocols::pump::{
    RedisSubscriber,        // 处理器
    TransactionBuilder,     // 交易构建器
    Filter,                 // 筛选器
    Calculator,             // 计算器
    parse_trades_from_redis_bytes, // 解析函数
};
```

## 优势

1. **清晰的模块边界**: 所有Pump相关代码都在一个目录下
2. **易于维护**: 相关功能集中管理
3. **可扩展性**: 为添加新协议(如Bonk)提供了良好的结构
4. **职责分离**: 核心处理逻辑和工具函数分离