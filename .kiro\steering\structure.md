# Project Structure

## Repository Organization

This is a multi-project repository containing several related components for the Solana trading system:

### Root Level Structure
```
├── admin/                          # Admin backend and frontend (submodule)
├── bonk构建参考代码/                # Bonk protocol reference implementation
├── new2.0qianduan/                 # Frontend v2.0 (React/TypeScript)
├── newgrpc/                        # gRPC services
├── qingqikaifaxxxxxxxxxxxxxxxxx/   # Main trading backend
├── taoli/                          # PumpFun parser and data processor
├── Dockerfile.all-in-one           # Complete system containerization
└── 交易系统-20分钟自毁版-快速上手.md    # Quick start documentation
```

### Key Components

#### Main Trading Backend (`qingqikaifaxxxxxxxxxxxxxxxxx/`)
- Contains the core copy-trading logic
- Handles transaction execution and monitoring
- Implements hot path optimizations for low-latency trading

#### Data Processing (`taoli/`)
- **Purpose**: Real-time Solana blockchain data parsing
- **Structure**:
  ```
  taoli/
  ├── src/
  │   ├── lib.rs              # Library entry point
  │   └── main.rs             # Demo application
  ├── config.toml             # gRPC and Redis configuration
  └── Cargo.toml              # Dependencies and metadata
  ```
- **Key Features**: PumpFun CPI event parsing, Redis publishing, gRPC streaming

#### Frontend (`new2.0qianduan/wallet-admin-frontend/`)
- React-based admin interface
- Wallet configuration and monitoring
- Real-time trading dashboard

#### Reference Implementations
- **`bonk构建参考代码/`**: Bonk protocol integration examples
- **`admin/`**: Administrative tools and interfaces

### Configuration Files
- **`config.toml`**: Service-specific configurations (gRPC endpoints, Redis settings)
- **`Cargo.toml`**: Rust project dependencies and metadata
- **`package.json`**: Frontend dependencies and build scripts

### Docker Architecture
The `Dockerfile.all-in-one` implements a multi-stage build:
1. **Rust Builder Stage**: Compiles all Rust components
2. **Frontend Builder Stage**: Builds React application
3. **Runtime Stage**: Ubuntu-based container with Nginx, Redis, and Supervisor

### Development Workflow
1. **Backend Development**: Work in individual Rust project directories
2. **Frontend Development**: Use `new2.0qianduan/wallet-admin-frontend/`
3. **Integration Testing**: Use Docker all-in-one build
4. **Deployment**: Container-based deployment with self-destruct capability

### Module Dependencies
- **Shared Types**: Common data structures across components
- **Protocol Handlers**: Separate modules for PumpFun and Bonk protocols
- **Hot Path Components**: Optimized modules for real-time trading execution
- **Service Layer**: Transaction execution, monitoring, and management services

### Naming Conventions
- **Chinese Directory Names**: Some directories use Chinese names for specific regional requirements
- **Rust Conventions**: snake_case for modules and functions
- **TypeScript Conventions**: camelCase for frontend components
- **Configuration**: TOML format for Rust services, JSON for frontend