use anyhow::{Result, anyhow};
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::Instruction,
    message::Message,
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use std::str::FromStr;
use std::sync::Arc;
use crate::shared::types::WalletConfig;
use crate::protocols::bonk::core::parser::BonkTradingData;
use crate::blockhash_service::BlockhashService;
use tracing::{info, debug};

// 复用bonk参考代码的常量
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
const RAYDIUM_LAUNCHPAD_PROGRAM: &str = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj";

pub struct BonkTransactionBuilder {
    wallet: Arc<Keypair>,
    blockhash_service: Arc<BlockhashService>,
    rpc_client: Arc<RpcClient>,
}

impl BonkTransactionBuilder {
    pub fn new(
        wallet: Arc<Keypair>, 
        blockhash_service: Arc<BlockhashService>,
        rpc_url: &str
    ) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new_with_commitment(
            rpc_url.to_string(),
            CommitmentConfig::confirmed(),
        ));
        
        Ok(Self {
            wallet,
            blockhash_service,
            rpc_client,
        })
    }
    
    /// 构建Bonk买入交易（100%复制bonk参考代码的execute_buy逻辑）
    pub fn build_buy_transaction(
        &self,
        bonk_data: &BonkTradingData,
        buy_amount_sol: f64,
        wallet_config: &WalletConfig,
    ) -> Result<Transaction> {
        info!("开始构建Bonk买入交易: {} SOL", buy_amount_sol);
        
        // 完全复用bonk参考代码的execute_buy逻辑
        let wsol_amount = (buy_amount_sol * 1_000_000_000.0) as u64; // 转换为lamports
        
        // 基础参数
        let pool_id = Pubkey::from_str(&bonk_data.pool_state)
            .map_err(|e| anyhow!("无效的pool_state地址: {}", e))?;
        let token_mint = Pubkey::from_str(&bonk_data.mint_address)
            .map_err(|e| anyhow!("无效的mint地址: {}", e))?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        
        // 从Redis直接获取的金库地址
        let base_vault = Pubkey::from_str(&bonk_data.pool_base_vault)
            .map_err(|e| anyhow!("无效的pool_base_vault地址: {}", e))?;
        let quote_vault = Pubkey::from_str(&bonk_data.pool_quote_vault)
            .map_err(|e| anyhow!("无效的pool_quote_vault地址: {}", e))?;
        
        // 计算用户的关联Token账户地址（可以直接计算）
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &wsol_mint
        );
        let user_token_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &token_mint
        );
        
        // 计算期望输出（基于Redis数据的比例）
        let minimum_amount_out = if bonk_data.amount_out > 0 {
            let ratio = wsol_amount as f64 / bonk_data.amount_in as f64;
            let expected_out = ratio * bonk_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        debug!("Bonk交易参数: wsol_amount={}, minimum_amount_out={}", 
            wsol_amount, minimum_amount_out);
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算 - 应用API配置参数
        let compute_limit = wallet_config.compute_unit_limit;
        let priority_fee = wallet_config.priority_fee;
        
        // 应用加速器tip配置
        let accelerator_tip = wallet_config.accelerator_tip_percentage.unwrap_or(1.0);
        let final_priority_fee = (priority_fee as f64 * (1.0 + accelerator_tip / 100.0)) as u64;
        
        debug!("应用Bonk交易配置: compute_limit={}, base_priority_fee={}, accelerator_tip={}%, final_priority_fee={}", 
            compute_limit, priority_fee, accelerator_tip, final_priority_fee);
        
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(final_priority_fee));
        
        // 2. 创建代币账户（幂等操作）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &wsol_mint,
                &spl_token::id(),
            )
        );
        
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &token_mint,
                &spl_token::id(),
            )
        );
        
        // 3. SOL转到WSOL账户
        instructions.push(system_instruction::transfer(
            &self.wallet.pubkey(),
            &user_wsol_account,
            wsol_amount,
        ));
        
        // 4. 同步WSOL账户
        instructions.push(spl_token::instruction::sync_native(
            &spl_token::id(), 
            &user_wsol_account
        )?);
        
        // 5. 构建Raydium Launchpad交易指令
        let raydium_launchpad_program = Pubkey::from_str(RAYDIUM_LAUNCHPAD_PROGRAM)?;
        
        // 构建buy_exact_in指令数据
        let mut swap_data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // buy_exact_in discriminator
        swap_data.extend_from_slice(&wsol_amount.to_le_bytes());
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes());
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 计算必要的PDA账户（完全按照bonk参考代码）
        let launchpad_program_id = raydium_launchpad_program;
        let authority_seed = b"vault_auth_seed";
        let event_authority_seed = b"__event_authority";
        
        let (authority, _) = Pubkey::find_program_address(&[authority_seed], &launchpad_program_id);
        let (event_authority, _) = Pubkey::find_program_address(&[event_authority_seed], &launchpad_program_id);
        
        // 使用固定配置地址（基于bonk参考代码）
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        // 构建Raydium Launchpad 15个账户列表（完全按照bonk参考代码顺序）
        let accounts = vec![
            // 0. payer (user wallet)
            solana_sdk::instruction::AccountMeta::new_readonly(self.wallet.pubkey(), true),
            // 1. authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(authority, false),
            // 2. global_config
            solana_sdk::instruction::AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            solana_sdk::instruction::AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户)
            solana_sdk::instruction::AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户)
            solana_sdk::instruction::AccountMeta::new(user_wsol_account, false),
            // 7. base_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(base_vault, false),
            // 8. quote_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            solana_sdk::instruction::AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            solana_sdk::instruction::AccountMeta::new_readonly(wsol_mint, false),
            // 11. base_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            solana_sdk::instruction::AccountMeta::new_readonly(raydium_launchpad_program, false),
        ];
        
        instructions.push(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        });
        
        // 6. 关闭WSOL账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.wallet.pubkey(),
            &self.wallet.pubkey(),
            &[],
        )?);
        
        // 构建最终交易
        let recent_blockhash_data = self.blockhash_service.get_latest_blockhash()
            .ok_or_else(|| anyhow!("无法获取最新区块哈希"))?;
        let recent_blockhash = recent_blockhash_data.hash;
        
        let message = Message::new(&instructions, Some(&self.wallet.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&*self.wallet], recent_blockhash);
        
        info!("Bonk买入交易构建完成: {} SOL -> {} tokens (最小输出)", 
            buy_amount_sol, minimum_amount_out);
        
        Ok(transaction)
    }
    
    /// 构建Bonk卖出交易（100%复制bonk参考代码的execute_sell逻辑）
    pub fn build_sell_transaction(
        &self,
        bonk_data: &BonkTradingData,
        token_amount: u64,
        wallet_config: &WalletConfig,
    ) -> Result<Transaction> {
        info!("开始构建Bonk卖出交易: {} tokens", token_amount);
        
        // 基础参数
        let pool_id = Pubkey::from_str(&bonk_data.pool_state)?;
        let token_mint = Pubkey::from_str(&bonk_data.mint_address)?;
        let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
        
        // 从Redis直接获取的金库地址
        let base_vault = Pubkey::from_str(&bonk_data.pool_base_vault)?;
        let quote_vault = Pubkey::from_str(&bonk_data.pool_quote_vault)?;
        
        // 计算用户的关联Token账户地址
        let user_wsol_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &wsol_mint
        );
        let user_token_account = spl_associated_token_account::get_associated_token_address(
            &self.wallet.pubkey(), &token_mint
        );
        
        // 计算期望输出（基于Redis数据的比例）
        let minimum_amount_out = if bonk_data.amount_in > 0 && bonk_data.amount_out > 0 {
            let ratio = token_amount as f64 / bonk_data.amount_in as f64;
            let expected_out = ratio * bonk_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        debug!("Bonk卖出参数: token_amount={}, minimum_amount_out={}", 
            token_amount, minimum_amount_out);
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        let compute_limit = wallet_config.compute_unit_limit;
        let priority_fee = wallet_config.priority_fee;
        let accelerator_tip = wallet_config.accelerator_tip_percentage.unwrap_or(1.0);
        let final_priority_fee = (priority_fee as f64 * (1.0 + accelerator_tip / 100.0)) as u64;
        
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(compute_limit));
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(final_priority_fee));
        
        // 2. 创建WSOL账户（如果不存在，用于接收卖出的SOL）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &wsol_mint,
                &spl_token::id(),
            )
        );
        
        // 3. 构建Raydium Launchpad卖出指令
        let raydium_launchpad_program = Pubkey::from_str(RAYDIUM_LAUNCHPAD_PROGRAM)?;
        
        // Raydium Launchpad sell_exact_in指令数据
        let mut swap_data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // sell_exact_in discriminator
        swap_data.extend_from_slice(&token_amount.to_le_bytes()); // amount_in (全部Token)
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes()); // minimum_amount_out  
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 计算必要的PDA账户
        let (authority, _) = Pubkey::find_program_address(
            &[b"vault_auth_seed"], 
            &raydium_launchpad_program
        );
        let (event_authority, _) = Pubkey::find_program_address(
            &[b"__event_authority"], 
            &raydium_launchpad_program
        );
        
        // 使用固定配置地址
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        // 构建Raydium Launchpad 15个账户列表（卖出顺序）
        let accounts = vec![
            // 0. payer (user wallet)
            solana_sdk::instruction::AccountMeta::new_readonly(self.wallet.pubkey(), true),
            // 1. authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(authority, false),
            // 2. global_config
            solana_sdk::instruction::AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            solana_sdk::instruction::AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户) - 卖出源
            solana_sdk::instruction::AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户) - 接收WSOL
            solana_sdk::instruction::AccountMeta::new(user_wsol_account, false),
            // 7. base_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(base_vault, false),
            // 8. quote_vault (从Redis获取)
            solana_sdk::instruction::AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            solana_sdk::instruction::AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            solana_sdk::instruction::AccountMeta::new_readonly(wsol_mint, false),
            // 11. base_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            solana_sdk::instruction::AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            solana_sdk::instruction::AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            solana_sdk::instruction::AccountMeta::new_readonly(raydium_launchpad_program, false),
        ];
        
        instructions.push(Instruction {
            program_id: raydium_launchpad_program,
            accounts,
            data: swap_data,
        });
        
        // 4. 卖出成功后unwrap WSOL为SOL
        instructions.push(spl_token::instruction::sync_native(&spl_token::id(), &user_wsol_account)?);
        
        // 5. 立即关闭WSOL账户赎回租金（原子操作）
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.wallet.pubkey(), // 租金接收者
            &self.wallet.pubkey(), // 账户所有者
            &[],
        )?);
        
        // 6. 关闭空的Token账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_token_account,
            &self.wallet.pubkey(), // 租金接收者
            &self.wallet.pubkey(), // 账户所有者
            &[],
        )?);
        
        // 构建最终交易
        let recent_blockhash_data = self.blockhash_service.get_latest_blockhash()
            .ok_or_else(|| anyhow!("无法获取最新区块哈希"))?;
        let recent_blockhash = recent_blockhash_data.hash;
        
        let message = Message::new(&instructions, Some(&self.wallet.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&*self.wallet], recent_blockhash);
        
        info!("Bonk卖出交易构建完成: {} tokens -> {} lamports (最小输出)", 
            token_amount, minimum_amount_out);
        
        Ok(transaction)
    }

    /// 构建并测试交易（真实数据但不发送）
    pub fn build_and_test_transaction(&self, transaction: &Transaction) -> Result<()> {
        info!("🔍 构建真实Bonk交易完成，准备测试（不发送）...");
        
        // 显示交易详情（像bonk参考代码一样）
        info!("✅ 交易构建成功!");
        info!("   🔗 交易签名: {}", transaction.signatures[0]);
        info!("   📋 指令数量: {}", transaction.message.instructions.len());
        info!("   🔑 账户数量: {}", transaction.message.account_keys.len());
        info!("   💰 交易已构建完成，但未发送到链上");
        
        Ok(())
    }

    /// 获取钱包公钥
    pub fn get_wallet_pubkey(&self) -> Pubkey {
        self.wallet.pubkey()
    }
}