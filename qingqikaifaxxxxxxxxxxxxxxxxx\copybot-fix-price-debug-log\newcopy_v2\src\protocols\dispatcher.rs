use std::sync::Arc;
use anyhow::Result;
use tracing::{info, debug};

use crate::shared::types::WalletConfig;
use crate::protocols::bonk::core::{BonkProcessor, is_bonk_data};

/// 简单的协议分发器 - 不做任何转换，直接分发原始数据
pub struct ProtocolDispatcher {
    bonk_processor: Arc<BonkProcessor>,
}

impl ProtocolDispatcher {
    pub fn new(bonk_processor: Arc<BonkProcessor>) -> Self {
        Self {
            bonk_processor,
        }
    }

    /// 分发Redis消息到对应的协议处理器
    pub async fn dispatch_message(
        &self,
        payload: &[u8],
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        debug!("🔍 协议分发器收到消息，长度: {} bytes", payload.len());
        
        // 检测协议类型（不做任何数据转换）
        if is_bonk_data(payload) {
            info!("📡 检测到Bonk协议数据，分发给Bonk处理器");
            self.bonk_processor.process_redis_message(payload, wallet_config).await?;
        } else {
            debug!("📡 未识别的协议数据，跳过");
        }
        
        Ok(())
    }
}

/// 协议类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum ProtocolType {
    Bonk,
    Pump,
    Unknown,
}

/// 快速协议检测（字节级检测，避免UTF-8转换）
pub fn detect_protocol_fast(payload: &[u8]) -> ProtocolType {
    // Bonk协议特征检测
    if is_bonk_data(payload) {
        return ProtocolType::Bonk;
    }
    
    // 可以在这里添加其他协议的检测
    // if is_pump_data(payload) {
    //     return ProtocolType::Pump;
    // }
    
    ProtocolType::Unknown
}