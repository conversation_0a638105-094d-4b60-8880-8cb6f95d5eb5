use anyhow::Result;
use tracing::{info, debug, warn, error};
use crate::protocols::bonk::utils::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, BonkCalculator, BonkCalculateInput,
    parse_bonk_trades_from_redis_bytes, BonkTransactionBuilder, BonkTradingData
};
use crate::services::transaction_sender::TransactionSender;
use crate::services::ata_cache::AtaCache;
use crate::shared::types::{TradeType, HotPathTrade};
use std::time::Instant;
use colored::Colorize;
use std::sync::Arc;
use arc_swap::ArcSwap;
use chrono::Local;
use dashmap::DashMap;
use tokio::sync::mpsc;
use crate::services::transaction_tracker::TransactionTracker;
use crate::services::transaction_tracker::TrackRequest;
use crate::services::price_broadcast::PriceBroadcastManager;
use crate::blockhash_service::BlockhashService;

/// 截断地址以方便显示
fn truncate_address(address: &str) -> String {
    if address.len() > 10 {
        format!("{}...{}", &address[..6], &address[address.len()-4..])
    } else {
        address.to_string()
    }
}

/// Bonk协议处理器，复用pump的架构
pub struct BonkRedisSubscriber {
    transaction_sender: Arc<TransactionSender>,
    ata_cache: Arc<AtaCache>,
    processed_sigs: Arc<DashMap<String, std::time::Instant>>,
    trade_log_tx: mpsc::Sender<HotPathTrade>,
    transaction_tracker: Arc<TransactionTracker>,
    tracker_tx: mpsc::Sender<TrackRequest>,
    price_broadcast_manager: PriceBroadcastManager,
    bonk_transaction_builder: Option<Arc<BonkTransactionBuilder>>,
}

impl BonkRedisSubscriber {
    /// 创建新的Bonk协议处理器
    pub async fn new(
        _redis_url: &str,
        transaction_sender: Arc<TransactionSender>,
        ata_cache: Arc<AtaCache>,
        trade_log_tx: mpsc::Sender<HotPathTrade>,
        transaction_tracker: Arc<TransactionTracker>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        price_broadcast_manager: PriceBroadcastManager,
    ) -> Result<Self> {
        info!("正在初始化Bonk协议处理器");
        
        Ok(Self {
            transaction_sender,
            ata_cache,
            processed_sigs: Arc::new(DashMap::new()),
            trade_log_tx,
            transaction_tracker,
            tracker_tx,
            price_broadcast_manager,
            bonk_transaction_builder: None, // 暂时为None，后续可以通过set_transaction_builder设置
        })
    }
    
    /// 设置Bonk交易构建器
    pub fn set_transaction_builder(&mut self, builder: Arc<BonkTransactionBuilder>) {
        self.bonk_transaction_builder = Some(builder);
        info!("Bonk交易构建器已设置");
    }
    
    /// 启动处理来自分发器的交易，复用pump的逻辑
    pub async fn start_processing(
        &self,
        filter: Arc<ArcSwap<BonkFilter>>,
        mut rx: mpsc::UnboundedReceiver<Vec<HotPathTrade>>,
    ) -> Result<()> {
        info!("Bonk协议处理器已启动，等待分发器消息");
        
        let calculator = BonkCalculator::new();

        while let Some(trades) = rx.recv().await {
            if !trades.is_empty() {
                debug!("接收到分发器消息: {} 笔Bonk交易", trades.len());
                
                for trade in trades {
                    let total_process_start_for_trade = Instant::now();
                    
                    // 签名去重
                    if !trade.signature.is_empty() {
                        const TTL_SECS: u64 = 120;
                        let now = std::time::Instant::now();
                        if let Some(entry) = self.processed_sigs.get(&trade.signature) {
                            if now.duration_since(*entry.value()) < std::time::Duration::from_secs(TTL_SECS) {
                                continue;
                            }
                        }
                        self.processed_sigs.insert(trade.signature.clone(), now);
                    }

                    // 广播价格更新
                    self.price_broadcast_manager.broadcast(trade.mint_pubkey, trade.price);

                    // 获取筛选器配置
                    let current_filter = filter.load();

                    // 通用筛选
                    if let Some(config) = current_filter.get_config_if_tracked(&trade) {
                        match trade.trade_type {
                            TradeType::Sell => {
                                // 卖出交易日志
                                let decimals = 5; // Bonk通常是5位小数
                                let ui_token_amount = trade.token_amount as f64 / 10f64.powi(decimals);
                                
                                let token_str = format!("{} BONK", ui_token_amount).red();
                                let sol_str = format!("{:.6} SOL", trade.sol_cost).red();

                                info!(
                                    "观察到Bonk卖出: {}, 获得 {} (来自跟踪钱包: {}, 签名: {})",
                                    token_str,
                                    sol_str,
                                    truncate_address(&trade.signer),
                                    trade.signature
                                );
                            }
                            TradeType::Buy => {
                                // 买入交易处理
                                let filter_start = Instant::now();
                                if let Some(buy_amount_sol) = current_filter.check_and_get_buy_details(&trade, config) {
                                    let filter_duration = filter_start.elapsed();
                                    
                                    let calculation_start = Instant::now();
                                    let input = BonkCalculateInput {
                                        buy_amount_sol,
                                        trade_price: trade.price,
                                        config,
                                    };
                                    let calculation_result = calculator.calculate(input);
                                    let calculation_duration = calculation_start.elapsed();
                                    
                                    if calculation_result.should_trade {
                                        let ui_token_amount = trade.token_amount / 10u64.pow(5); // Bonk的decimals

                                        let ts = Local::now().format("%H:%M:%S.%3f");
                                        info!(
                                            "{} 收到Bonk买入交易 (原始: {} SOL / {} BONK, 来自钱包: {}, 签名: {})",
                                            ts,
                                            trade.sol_cost.to_string().cyan(),
                                            ui_token_amount.to_string().cyan(),
                                            truncate_address(&trade.signer).yellow(),
                                            trade.signature.blue()
                                        );

                                        let mode_text = match config.follow_mode {
                                            crate::shared::types::FollowMode::Percentage => "Bonk百分比跟单",
                                            crate::shared::types::FollowMode::FixedAmount => "Bonk固定金额跟单",
                                        };

                                        let total_duration = total_process_start_for_trade.elapsed();

                                        warn!(
                                            "{}: 目标代币 {}, 最大花费 {} SOL | 延迟: {:?} (筛选: {:?}, 计算: {:?})",
                                            mode_text,
                                            calculation_result.min_expected_token_amount_out.to_string().green(),
                                            calculation_result.final_buy_amount_sol.to_string().green(),
                                            total_duration,
                                            filter_duration,
                                            calculation_duration
                                        );

                                        // TODO: 这里需要实现Bonk的交易构建逻辑
                                        // 现在先记录，等待交易构建器实现
                                        info!("✅ Bonk交易已处理 - 等待构建器实现: {}", trade.signature.blue());

                                    } else {
                                        debug!("Bonk交易在计算阶段被拒绝 (签名者: {}), 原因: {}", trade.signer, calculation_result.reason);
                                    }
                                } else {
                                    debug!(
                                        "Bonk购买交易被拒绝 (签名者: {}, MINT: {}), 价格: {}",
                                        trade.signer, trade.mint_pubkey.to_string(), trade.price
                                    );
                                }
                            }
                            TradeType::Unknown => {
                                // 忽略未知类型
                            }
                        }
                    }
                }
            }
        }
        Ok(())
    }
}