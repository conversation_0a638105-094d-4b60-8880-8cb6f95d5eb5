use anyhow::Result;
use tracing::{info, debug, warn, error};
use crate::protocols::bonk::utils::{
    <PERSON><PERSON><PERSON><PERSON><PERSON>, BonkCalculator, BonkCalculateInput,
    parse_bonk_trades_from_redis_bytes, BonkTransactionBuilder, BonkTradingData
};
use crate::services::transaction_sender::TransactionSender;
use crate::services::ata_cache::AtaCache;
use crate::shared::types::{TradeType, HotPathTrade};
use std::time::Instant;
use colored::Colorize;
use std::sync::Arc;
use arc_swap::ArcSwap;
use chrono::Local;
use dashmap::DashMap;
use tokio::sync::mpsc;
use crate::services::transaction_tracker::TransactionTracker;
use crate::services::transaction_tracker::TrackRequest;
use crate::services::price_broadcast::PriceBroadcastManager;
use crate::blockhash_service::BlockhashService;

/// 截断地址以方便显示
fn truncate_address(address: &str) -> String {
    if address.len() > 10 {
        format!("{}...{}", &address[..6], &address[address.len()-4..])
    } else {
        address.to_string()
    }
}

/// Bonk协议处理器，复用pump的架构
pub struct BonkRedisSubscriber {
    transaction_sender: Arc<TransactionSender>,
    ata_cache: Arc<AtaCache>,
    processed_sigs: Arc<DashMap<String, std::time::Instant>>,
    trade_log_tx: mpsc::Sender<HotPathTrade>,
    transaction_tracker: Arc<TransactionTracker>,
    tracker_tx: mpsc::Sender<TrackRequest>,
    price_broadcast_manager: PriceBroadcastManager,
    bonk_transaction_builder: Option<Arc<BonkTransactionBuilder>>,
}

impl BonkRedisSubscriber {
    /// 创建新的Bonk协议处理器
    pub async fn new(
        _redis_url: &str,
        transaction_sender: Arc<TransactionSender>,
        ata_cache: Arc<AtaCache>,
        trade_log_tx: mpsc::Sender<HotPathTrade>,
        transaction_tracker: Arc<TransactionTracker>,
        tracker_tx: mpsc::Sender<TrackRequest>,
        price_broadcast_manager: PriceBroadcastManager,
    ) -> Result<Self> {
        info!("正在初始化Bonk协议处理器");
        
        Ok(Self {
            transaction_sender,
            ata_cache,
            processed_sigs: Arc::new(DashMap::new()),
            trade_log_tx,
            transaction_tracker,
            tracker_tx,
            price_broadcast_manager,
            bonk_transaction_builder: None, // 暂时为None，后续可以通过set_transaction_builder设置
        })
    }
    
    /// 设置Bonk交易构建器
    pub fn set_transaction_builder(&mut self, builder: Arc<BonkTransactionBuilder>) {
        self.bonk_transaction_builder = Some(builder);
        info!("Bonk交易构建器已设置");
    }
    
    /// 启动处理来自分发器的交易，复用pump的逻辑
    pub async fn start_processing(
        &self,
        filter: Arc<ArcSwap<BonkFilter>>,
        mut rx: mpsc::UnboundedReceiver<Vec<HotPathTrade>>,
    ) -> Result<()> {
        info!("Bonk协议处理器已启动，等待分发器消息");
        
        let calculator = BonkCalculator::new();

        while let Some(trades) = rx.recv().await {
            if !trades.is_empty() {
                debug!("接收到分发器消息: {} 笔Bonk交易", trades.len());
                
                for trade in trades {
                    let total_process_start_for_trade = Instant::now();
                    
                    // 签名去重
                    if !trade.signature.is_empty() {
                        const TTL_SECS: u64 = 120;
                        let now = std::time::Instant::now();
                        if let Some(entry) = self.processed_sigs.get(&trade.signature) {
                            if now.duration_since(*entry.value()) < std::time::Duration::from_secs(TTL_SECS) {
                                continue;
                            }
                        }
                        self.processed_sigs.insert(trade.signature.clone(), now);
                    }

                    // 广播价格更新
                    self.price_broadcast_manager.broadcast(trade.mint_pubkey, trade.price);

                    // 获取筛选器配置
                    let current_filter = filter.load();

                    // 通用筛选
                    if let Some(config) = current_filter.get_config_if_tracked(&trade) {
                        match trade.trade_type {
                            TradeType::Sell => {
                                // 卖出交易日志
                                let decimals = 5; // Bonk通常是5位小数
                                let ui_token_amount = trade.token_amount as f64 / 10f64.powi(decimals);
                                
                                let token_str = format!("{} BONK", ui_token_amount).red();
                                let sol_str = format!("{:.6} SOL", trade.sol_cost).red();

                                info!(
                                    "观察到Bonk卖出: {}, 获得 {} (来自跟踪钱包: {}, 签名: {})",
                                    token_str,
                                    sol_str,
                                    truncate_address(&trade.signer),
                                    trade.signature
                                );
                            }
                            TradeType::Buy => {
                                // 买入交易处理
                                let filter_start = Instant::now();
                                if let Some(buy_amount_sol) = current_filter.check_and_get_buy_details(&trade, config) {
                                    let filter_duration = filter_start.elapsed();
                                    
                                    let calculation_start = Instant::now();
                                    let input = BonkCalculateInput {
                                        buy_amount_sol,
                                        trade_price: trade.price,
                                        config,
                                    };
                                    let calculation_result = calculator.calculate(input);
                                    let calculation_duration = calculation_start.elapsed();
                                    
                                    if calculation_result.should_trade {
                                        let ui_token_amount = trade.token_amount / 10u64.pow(5); // Bonk的decimals

                                        let ts = Local::now().format("%H:%M:%S.%3f");
                                        info!(
                                            "{} 收到Bonk买入交易 (原始: {} SOL / {} BONK, 来自钱包: {}, 签名: {})",
                                            ts,
                                            trade.sol_cost.to_string().cyan(),
                                            ui_token_amount.to_string().cyan(),
                                            truncate_address(&trade.signer).yellow(),
                                            trade.signature.blue()
                                        );

                                        let mode_text = match config.follow_mode {
                                            crate::shared::types::FollowMode::Percentage => "Bonk百分比跟单",
                                            crate::shared::types::FollowMode::FixedAmount => "Bonk固定金额跟单",
                                        };

                                        let total_duration = total_process_start_for_trade.elapsed();

                                        warn!(
                                            "{}: 目标代币 {}, 最大花费 {} SOL | 延迟: {:?} (筛选: {:?}, 计算: {:?})",
                                            mode_text,
                                            calculation_result.min_expected_token_amount_out.to_string().green(),
                                            calculation_result.final_buy_amount_sol.to_string().green(),
                                            total_duration,
                                            filter_duration,
                                            calculation_duration
                                        );

                                        // 实现Bonk交易构建逻辑
                                        if let Some(ref builder) = self.bonk_transaction_builder {
                                            self.handle_bonk_buy_trade(
                                                &trade,
                                                config,
                                                calculation_result.final_buy_amount_sol,
                                                builder.clone(),
                                            ).await;
                                        } else {
                                            warn!("⚠️ Bonk交易构建器未设置，跳过交易构建: {}", trade.signature.blue());
                                        }

                                    } else {
                                        debug!("Bonk交易在计算阶段被拒绝 (签名者: {}), 原因: {}", trade.signer, calculation_result.reason);
                                    }
                                } else {
                                    debug!(
                                        "Bonk购买交易被拒绝 (签名者: {}, MINT: {}), 价格: {}",
                                        trade.signer, trade.mint_pubkey.to_string(), trade.price
                                    );
                                }
                            }
                            TradeType::Unknown => {
                                // 忽略未知类型
                            }
                        }
                    }
                }
            }
        }
        Ok(())
    }
    
    /// 处理Bonk买入交易
    async fn handle_bonk_buy_trade(
        &self,
        trade: &HotPathTrade,
        config: &crate::shared::types::WalletConfig,
        buy_amount_sol: f64,
        builder: Arc<BonkTransactionBuilder>,
    ) {
        let build_start = Instant::now();
        
        // 首先需要从原始交易数据中提取BonkTradingData
        // 这里我们需要模拟一个BonkTradingData结构，因为HotPathTrade没有包含所有必要信息
        let bonk_data = BonkTradingData {
            signature: trade.signature.clone(),
            pool_state: trade.bonding_curve_pubkey.to_string(), // 使用bonding_curve作为pool_state
            signer: trade.signer.clone(),
            mint_address: trade.mint_pubkey.to_string(),
            total_base_sell: 0,
            virtual_base: 0,
            virtual_quote: 0,
            real_base_before: 0,
            real_quote_before: 0,
            real_base_after: 0,
            real_quote_after: 0,
            amount_in: (trade.sol_cost * 1_000_000_000.0) as u64, // SOL转lamports
            amount_out: trade.token_amount,
            protocol_fee: 0,
            platform_fee: 0,
            share_fee: 0,
            trade_direction: "Buy".to_string(),
            pool_status: "active".to_string(),
            price_before: trade.price,
            price_after: trade.price,
            slippage: config.slippage_percentage,
            pool_base_vault: trade.creator_vault_pubkey.to_string(), // 临时使用
            pool_quote_vault: trade.user_ata.to_string(), // 临时使用
        };
        
        // 验证交易参数
        if let Err(e) = builder.validate_bonk_trade_params(&bonk_data, buy_amount_sol) {
            error!("Bonk交易参数验证失败: {}", e);
            return;
        }
        
        // 构建交易
        match builder.build_buy_transaction(&bonk_data, buy_amount_sol, config) {
            Ok(transaction) => {
                let build_duration = build_start.elapsed();
                
                info!(
                    "✅ Bonk交易构建成功: {} SOL -> {} (构建耗时: {:?})",
                    buy_amount_sol.to_string().green(),
                    trade.mint_pubkey.to_string().blue(),
                    build_duration
                );
                
                // 记录交易到日志
                if let Err(e) = self.trade_log_tx.send(trade.clone()).await {
                    warn!("发送Bonk交易日志失败: {}", e);
                }
                
                // 这里暂时不发送交易，只是构建和验证
                // 后续可以通过配置控制是否实际发送
                info!("🔄 Bonk交易已构建完成，暂不发送 (签名: {})", trade.signature.blue());
                
                // 可选：将交易添加到跟踪器（如果需要模拟跟踪）
                // let track_request = TrackRequest {
                //     signature: transaction.signatures[0],
                //     trade: trade.clone(),
                //     config: config.clone(),
                // };
                // if let Err(e) = self.tracker_tx.send(track_request).await {
                //     warn!("发送Bonk交易跟踪请求失败: {}", e);
                // }
                
            }
            Err(e) => {
                let build_duration = build_start.elapsed();
                error!(
                    "❌ Bonk交易构建失败: {} (耗时: {:?})",
                    e,
                    build_duration
                );
            }
        }
    }
    
    /// 从Redis字节数据直接处理Bonk交易（用于直接订阅模式）
    pub async fn process_bonk_redis_data(
        &self,
        payload: &[u8],
        filter: &Arc<ArcSwap<BonkFilter>>,
    ) -> Result<()> {
        let user_wallet_pubkey = if let Some(ref builder) = self.bonk_transaction_builder {
            builder.get_wallet_pubkey()
        } else {
            warn!("Bonk交易构建器未设置，无法处理Redis数据");
            return Ok(());
        };
        
        let trades = parse_bonk_trades_from_redis_bytes(payload, &user_wallet_pubkey);
        
        if !trades.is_empty() {
            debug!("从Redis解析到 {} 笔Bonk交易", trades.len());
            
            // 将解析的交易发送到处理管道
            self.start_processing(filter.clone(), {
                let (tx, rx) = mpsc::unbounded_channel();
                tx.send(trades).unwrap();
                rx
            }).await?;
        }
        
        Ok(())
    }
}