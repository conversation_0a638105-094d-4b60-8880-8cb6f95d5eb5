use std::sync::Arc;
use anyhow::Result;
use tracing::{info, debug, error};

use crate::shared::types::WalletConfig;
use crate::protocols::bonk::core::{BonkTradingData, BonkTransactionBuilder, parse_bonk_data_from_redis};

/// Bonk协议处理器 - 与Pump架构一致
pub struct BonkProcessor {
    transaction_builder: Arc<BonkTransactionBuilder>,
}

impl BonkProcessor {
    pub fn new(transaction_builder: Arc<BonkTransactionBuilder>) -> Self {
        Self {
            transaction_builder,
        }
    }

    /// 处理Bonk买入交易（构建但不发送）
    pub async fn process_buy_trade(
        &self,
        bonk_data: &BonkTradingData,
        buy_amount_sol: f64,
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        info!("🔥 处理Bonk买入交易: {} SOL", buy_amount_sol);
        
        // 构建交易
        let transaction = self.transaction_builder.build_buy_transaction(
            bonk_data,
            buy_amount_sol,
            wallet_config,
        )?;
        
        // 测试交易（不发送）
        self.transaction_builder.build_and_test_transaction(&transaction)?;
        
        info!("✅ Bonk买入交易处理完成（已构建但未发送）");
        Ok(())
    }

    /// 处理Bonk卖出交易（构建但不发送）
    pub async fn process_sell_trade(
        &self,
        bonk_data: &BonkTradingData,
        token_amount: u64,
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        info!("🔥 处理Bonk卖出交易: {} tokens", token_amount);
        
        // 构建交易
        let transaction = self.transaction_builder.build_sell_transaction(
            bonk_data,
            token_amount,
            wallet_config,
        )?;
        
        // 测试交易（不发送）
        self.transaction_builder.build_and_test_transaction(&transaction)?;
        
        info!("✅ Bonk卖出交易处理完成（已构建但未发送）");
        Ok(())
    }

    /// 处理Redis消息
    pub async fn process_redis_message(
        &self,
        payload: &[u8],
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        debug!("🔍 处理Bonk Redis消息...");
        
        // 解析数据
        if let Some(bonk_data) = parse_bonk_data_from_redis(payload) {
            info!("📊 解析到Bonk交易: {} ({}) - {} -> {}", 
                bonk_data.signature, bonk_data.trade_direction, 
                bonk_data.mint_address, bonk_data.signer);
            
            // 根据交易方向处理
            match bonk_data.trade_direction.as_str() {
                "Buy" => {
                    // 这里可以根据配置决定跟单金额
                    let follow_amount = 0.001; // 示例：固定0.001 SOL
                    self.process_buy_trade(&bonk_data, follow_amount, wallet_config).await?;
                }
                "Sell" => {
                    // 这里可以根据配置决定卖出数量
                    let sell_amount = bonk_data.amount_in; // 示例：使用原始数量
                    self.process_sell_trade(&bonk_data, sell_amount, wallet_config).await?;
                }
                _ => {
                    debug!("跳过未知交易方向: {}", bonk_data.trade_direction);
                }
            }
        } else {
            debug!("无法解析Bonk数据，跳过");
        }
        
        Ok(())
    }
}