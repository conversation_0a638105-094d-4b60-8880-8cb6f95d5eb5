use std::sync::Arc;
use anyhow::Result;
use tracing::{info, debug};

use crate::shared::types::WalletConfig;
use crate::protocols::bonk::core::{BonkTradingData, BonkTransactionBuilder, parse_bonk_data_from_redis};

/// Bonk协议处理器 - 与Pump架构一致
pub struct BonkProcessor {
    transaction_builder: Arc<BonkTransactionBuilder>,
}

impl BonkProcessor {
    pub fn new(transaction_builder: Arc<BonkTransactionBuilder>) -> Self {
        Self {
            transaction_builder,
        }
    }

    /// 处理Bonk买入交易（构建但不发送）
    pub async fn process_buy_trade(
        &self,
        bonk_data: &BonkTradingData,
        buy_amount_sol: f64,
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        info!("🔥 处理Bonk买入交易: {} SOL", buy_amount_sol);
        
        // 构建交易
        let transaction = self.transaction_builder.build_buy_transaction(
            bonk_data,
            buy_amount_sol,
            wallet_config,
        )?;
        
        // 测试交易（不发送）
        self.transaction_builder.build_and_test_transaction(&transaction)?;
        
        info!("✅ Bonk买入交易处理完成（已构建但未发送）");
        Ok(())
    }

    /// 处理Bonk卖出交易（构建但不发送）
    pub async fn process_sell_trade(
        &self,
        bonk_data: &BonkTradingData,
        token_amount: u64,
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        info!("🔥 处理Bonk卖出交易: {} tokens", token_amount);
        
        // 构建交易
        let transaction = self.transaction_builder.build_sell_transaction(
            bonk_data,
            token_amount,
            wallet_config,
        )?;
        
        // 测试交易（不发送）
        self.transaction_builder.build_and_test_transaction(&transaction)?;
        
        info!("✅ Bonk卖出交易处理完成（已构建但未发送）");
        Ok(())
    }

    /// 处理Redis消息
    pub async fn process_redis_message(
        &self,
        payload: &[u8],
        wallet_config: &WalletConfig,
    ) -> Result<()> {
        debug!("🔍 处理Bonk Redis消息...");
        
        // 解析数据
        if let Some(bonk_data) = parse_bonk_data_from_redis(payload) {
            info!("📊 解析到Bonk交易: {} ({}) - {} -> {}", 
                bonk_data.signature, bonk_data.trade_direction, 
                bonk_data.mint_address, bonk_data.signer);
            
            // 先进行筛选检查
            if !self.should_follow_trade(&bonk_data, wallet_config) {
                debug!("🚫 Bonk交易未通过筛选条件，跳过处理");
                return Ok(());
            }

            // 根据交易方向处理
            match bonk_data.trade_direction.as_str() {
                "Buy" => {
                    // 根据配置计算跟单金额
                    if let Some(follow_amount) = self.calculate_follow_amount(&bonk_data, wallet_config) {
                        info!("💰 Bonk买入跟单金额: {} SOL", follow_amount);
                        self.process_buy_trade(&bonk_data, follow_amount, wallet_config).await?;
                    } else {
                        debug!("🚫 Bonk买入金额计算失败，跳过");
                    }
                }
                "Sell" => {
                    // 根据配置计算卖出数量
                    if let Some(sell_amount) = self.calculate_sell_amount(&bonk_data, wallet_config) {
                        info!("💰 Bonk卖出数量: {} tokens", sell_amount);
                        self.process_sell_trade(&bonk_data, sell_amount, wallet_config).await?;
                    } else {
                        debug!("🚫 Bonk卖出数量计算失败，跳过");
                    }
                }
                _ => {
                    debug!("跳过未知交易方向: {}", bonk_data.trade_direction);
                }
            }
        } else {
            debug!("无法解析Bonk数据，跳过");
        }
        
        Ok(())
    }

    /// 检查是否应该跟单此交易
    fn should_follow_trade(&self, bonk_data: &BonkTradingData, wallet_config: &WalletConfig) -> bool {
        // 1. 检查交易金额范围
        let trade_sol_amount = bonk_data.amount_in as f64 / 1_000_000_000.0; // lamports to SOL

        if let Some(min_sol) = wallet_config.sol_amount_min {
            if trade_sol_amount < min_sol {
                debug!("🚫 交易金额 {} SOL 低于最小值 {} SOL", trade_sol_amount, min_sol);
                return false;
            }
        }

        if let Some(max_sol) = wallet_config.sol_amount_max {
            if trade_sol_amount > max_sol {
                debug!("🚫 交易金额 {} SOL 高于最大值 {} SOL", trade_sol_amount, max_sol);
                return false;
            }
        }

        // 2. 检查价格范围
        if let Some(min_price) = wallet_config.min_price_multiplier {
            if bonk_data.price_after < min_price {
                debug!("🚫 代币价格 {} 低于最小值 {}", bonk_data.price_after, min_price);
                return false;
            }
        }

        if let Some(max_price) = wallet_config.max_price_multiplier {
            if bonk_data.price_after > max_price {
                debug!("🚫 代币价格 {} 高于最大值 {}", bonk_data.price_after, max_price);
                return false;
            }
        }

        info!("✅ Bonk交易通过筛选: 金额={} SOL, 价格={}", trade_sol_amount, bonk_data.price_after);
        true
    }

    /// 计算跟单金额
    fn calculate_follow_amount(&self, _bonk_data: &BonkTradingData, wallet_config: &WalletConfig) -> Option<f64> {
        match wallet_config.follow_mode {
            FollowMode::FixedAmount => {
                wallet_config.fixed_follow_amount_sol
            }
            FollowMode::Percentage => {
                // TODO: 实现百分比跟单
                debug!("百分比跟单模式暂未实现");
                None
            }
        }
    }

    /// 计算卖出数量
    fn calculate_sell_amount(&self, bonk_data: &BonkTradingData, _wallet_config: &WalletConfig) -> Option<u64> {
        // 简单实现：使用原始交易的数量
        // TODO: 根据持仓情况计算实际卖出数量
        Some(bonk_data.amount_in)
    }
}