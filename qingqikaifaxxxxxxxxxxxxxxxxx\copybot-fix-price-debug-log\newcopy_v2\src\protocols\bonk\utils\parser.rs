use crate::shared::types::{HotPathTrade, TradeType};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use tracing::{debug, error, warn};
use serde::{Deserialize, Serialize};

/// Bonk交易数据结构（基于bonk参考代码）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BonkTradingData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

impl TryFrom<BonkTradingData> for HotPathTrade {
    type Error = anyhow::Error;

    fn try_from(bonk_data: BonkTradingData) -> Result<Self, Self::Error> {
        let trade_type = match bonk_data.trade_direction.as_str() {
            "Buy" => TradeType::Buy,
            "Sell" => TradeType::Sell,
            _ => TradeType::Unknown,
        };

        let mint_pubkey = Pubkey::from_str(&bonk_data.mint_address)?;
        let signer_pubkey = Pubkey::from_str(&bonk_data.signer)?;

        // 计算SOL成本（基于amount_in和price）
        let sol_cost = if bonk_data.amount_in > 0 {
            bonk_data.amount_in as f64 / 1_000_000_000.0 // 转换为SOL
        } else {
            0.0
        };

        // 预计算账户地址（Bonk协议特定）
        let bonding_curve_pubkey = mint_pubkey; // 临时使用mint作为bonding curve
        let associated_bonding_curve = mint_pubkey; // 临时使用
        let user_ata = spl_associated_token_account::get_associated_token_address(
            &signer_pubkey, 
            &mint_pubkey
        );
        let creator_vault_pubkey = signer_pubkey; // 临时使用

        Ok(HotPathTrade {
            trade_id: uuid::Uuid::new_v4().to_string(),
            signature: bonk_data.signature,
            signer: bonk_data.signer,
            mint_pubkey,
            trade_type,
            token_amount: bonk_data.amount_out,
            sol_cost,
            price: bonk_data.price_after,
            bonding_curve_pubkey,
            associated_bonding_curve,
            user_ata,
            creator_vault_pubkey,
            slippage_bps: 0, // 默认值，后续可以从配置中获取
        })
    }
}

/// 解析bonk Redis数据为HotPathTrade格式
pub fn parse_bonk_trades_from_redis_bytes(
    payload: &[u8],
    _user_wallet_pubkey: &Pubkey,
) -> Vec<HotPathTrade> {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 尝试解析单个Bonk交易数据
        match serde_json::from_str::<BonkTradingData>(data_str) {
            Ok(bonk_data) => {
                // 跳过未知Token地址
                if bonk_data.mint_address == "unknown" || bonk_data.mint_address.is_empty() {
                    debug!("跳过未知Token地址的Bonk消息");
                    return vec![];
                }
                
                // 跳过无效签名
                if bonk_data.signature.is_empty() {
                    debug!("跳过无效签名的Bonk消息");
                    return vec![];
                }
                
                match bonk_data.try_into() {
                    Ok(trade) => {
                        debug!("成功解析Bonk交易: {} ({})", trade.signature, trade.trade_type as u8);
                        vec![trade]
                    }
                    Err(e) => {
                        error!("Bonk数据转换失败: {}", e);
                        vec![]
                    }
                }
            }
            Err(e) => {
                // 尝试解析数组格式
                match serde_json::from_str::<Vec<BonkTradingData>>(data_str) {
                    Ok(bonk_data_array) => {
                        let mut trades = Vec::new();
                        for bonk_data in bonk_data_array {
                            if bonk_data.mint_address == "unknown" || bonk_data.mint_address.is_empty() {
                                continue;
                            }
                            if bonk_data.signature.is_empty() {
                                continue;
                            }
                            
                            match bonk_data.try_into() {
                                Ok(trade) => trades.push(trade),
                                Err(e) => {
                                    error!("Bonk数组数据转换失败: {}", e);
                                }
                            }
                        }
                        debug!("成功解析Bonk交易数组: {} 笔交易", trades.len());
                        trades
                    }
                    Err(_) => {
                        error!("Bonk JSON解析失败: {}", e);
                        vec![]
                    }
                }
            }
        }
    } else {
        error!("Bonk数据UTF-8解码失败");
        vec![]
    }
}

/// 检测是否为bonk数据的改进版本
pub fn is_bonk_data(payload: &[u8]) -> bool {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 更精确的bonk数据特征检测
        data_str.contains("pool_state") && 
        data_str.contains("pool_base_vault") && 
        data_str.contains("pool_quote_vault") &&
        data_str.contains("trade_direction")
    } else {
        false
    }
}

/// 从Redis字节数据中提取Bonk交易的基本信息（用于快速筛选）
pub fn extract_bonk_trade_info(payload: &[u8]) -> Option<(String, String, f64)> {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        if let Ok(bonk_data) = serde_json::from_str::<BonkTradingData>(data_str) {
            return Some((
                bonk_data.signature,
                bonk_data.signer,
                bonk_data.price_after,
            ));
        }
    }
    None
}