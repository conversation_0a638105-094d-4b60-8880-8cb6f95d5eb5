use crate::shared::types::{WalletConfig, TipProvider};
use colored::Colorize;
use tracing::{info, warn};
use std::time::Duration;

// --- 1. 定义输入配置：钱包配置 ---
// 这个模块不再定义 WalletConfig，它现在从 `shared::types` 导入。

// --- 2. 定义输出结果：计算结果 ---
/// 存储热路径计算的直接输出。
/// 这个结构体是构建交易的"配方"。
#[derive(Debug, Clone)]
pub struct CalculationResult {
    /// 决策结果：是否应该执行交易。
    pub should_trade: bool,
    /// 决策的原因或备注，主要用于冷路径日志记录，帮助事后分析。
    pub reason: &'static str,

    // --- 以下字段仅在 should_trade 为 true 时有效 ---
    /// 最终计算出的本次交易最大愿意花费的 SOL 数量（包含滑点保护）。
    pub final_buy_amount_sol: f64,
    /// 目标购买的代币数量（Exact-Output模式）。
    pub min_expected_token_amount_out: u64,
    /// 交易要使用的优先费（单位: microlamports）。
    pub priority_fee: u64,
    /// 交易的计算单元限制。
    pub compute_unit_limit: u32,
    /// 通用小费详情。
    pub tip_details: Option<TipDetails>,
}

/// 包含小费提供商和具体金额的结构体。
#[derive(Debug, Clone)]
pub struct TipDetails {
    pub provider: TipProvider,
    pub amount_lamports: u64,
}

/// 作为 calculate 函数输入的结构体，包含了所有决策后用于计算的参数。
pub struct CalculateInput<'a> {
    /// 经过决策后，最终决定用于购买的SOL数量。
    pub buy_amount_sol: f64,
    /// 当前交易的单价。
    pub trade_price: f64,
    /// 与本次交易相关的钱包配置的引用。
    // 这里明确使用从 shared::types 导入的 WalletConfig
    pub config: &'a WalletConfig,
}

impl CalculationResult {
    /// 一个辅助函数，用于快速创建一个不交易的返回结果。
    pub fn do_not_trade(reason: &'static str) -> Self {
        Self {
            should_trade: false,
            reason,
            final_buy_amount_sol: 0.0,
            min_expected_token_amount_out: 0,
            priority_fee: 0,
            compute_unit_limit: 0,
            tip_details: None,
        }
    }
}

// --- 3. 定义计算器结构体 ---
/// 高性能计算器，是一个无状态的命名空间。
/// 它现在只负责纯粹的数学计算，不进行任何决策。
#[derive(Debug, Clone, Default)]
pub struct Calculator;

// --- 4. 实现计算器 ---
impl Calculator {
    /// 创建一个新的计算器实例。
    /// 因为计算器现在是无状态的，这个函数只是为了方便创建实例。
    pub fn new() -> Self {
        Self::default()
    }

    /// 核心热路径计算函数。
    /// 现在只接收决策后的参数，进行纯粹的计算。
    #[inline(always)]
    pub fn calculate(&self, input: CalculateInput) -> CalculationResult {
        // 步骤1：计算目标代币数量（Exact-Output模式）
        let target_tokens = input.buy_amount_sol / input.trade_price;
        let target_token_amount_out = target_tokens as u64;

        if target_token_amount_out == 0 {
             return CalculationResult {
                should_trade: false,
                reason: "目标代币数量为0",
                final_buy_amount_sol: 0.0,
                min_expected_token_amount_out: 0,
                priority_fee: 0,
                compute_unit_limit: 0,
                tip_details: None,
            };
        }

        // 步骤2：计算最大SOL成本（加上滑点保护）
        let base_sol_cost = input.buy_amount_sol;
        let max_sol_cost = base_sol_cost * (1.0 + input.config.slippage_percentage / 100.0);

        // 步骤3：构建并返回成功的计算结果
        CalculationResult {
            should_trade: true,
            reason: "计算成功",
            final_buy_amount_sol: max_sol_cost, // 现在是最大SOL成本
            min_expected_token_amount_out: target_token_amount_out, // 现在是目标代币数
            priority_fee: input.config.priority_fee,
            compute_unit_limit: input.config.compute_unit_limit,
            tip_details: None,
        }
    }
}

// --- 5. 冷路径处理函数 ---

/// 将 Duration 格式化为带一位小数的微秒字符串
fn format_duration_as_us(d: Duration) -> String {
    format!("{:.1}µs", d.as_nanos() as f64 / 1000.0)
}

/// 处理非时间关键的后续任务，例如记录日志、持久化数据或发送通知。
/// 这个函数在热路径完成后被调用，其执行时间不影响核心交易延迟。
pub fn log_calculation_result(
    result: &CalculationResult,
    total_duration: Duration,
    filter_duration: Duration,
    calculation_duration: Duration,
    build_duration: Duration,
    build_to_send_delay: Duration,
) {
    let details = format!(
        "计算结果: 购买 {}, 最低接收 {} | 延迟: {} (筛选: {}, 计算: {}, 构建: {}, 构建→发送: {})",
        result.final_buy_amount_sol.to_string().green(),
        result.min_expected_token_amount_out.to_string().green(),
        format_duration_as_us(total_duration).red(),
        format_duration_as_us(filter_duration).yellow(),
        format_duration_as_us(calculation_duration).yellow(),
        format_duration_as_us(build_duration).yellow(),
        format_duration_as_us(build_to_send_delay).yellow()
    );

    if total_duration.as_micros() > 200 {
        warn!("{}", details);
    } else {
        info!("{}", details);
    }
} 