# Technology Stack

## Backend Architecture
- **Language**: <PERSON>ust (primary backend language)
- **Build System**: <PERSON><PERSON> (Rust package manager)
- **Runtime**: Toki<PERSON> (async runtime)
- **Database**: Redis (message queue and caching)
- **Web Server**: <PERSON>inx (reverse proxy and static file serving)
- **Process Management**: Supervisor (multi-service orchestration)

## Frontend
- **Framework**: React with TypeScript
- **Build Tool**: npm/Node.js 20
- **UI Library**: Ant Design
- **Package Manager**: npm

## Blockchain Integration
- **Platform**: Solana
- **RPC Client**: Solana Client SDK
- **Protocols**: <PERSON>ump<PERSON>un, Bonk (Raydium Launchpad)
- **gRPC**: Yellowstone gRPC for real-time data streaming
- **Token Standards**: SPL Token, Associated Token Accounts

## Key Dependencies
### Rust Backend
- `tokio` - Async runtime with full features
- `redis` - Redis client with tokio compatibility
- `serde` + `serde_json` - Serialization
- `solana-client`, `solana-sdk` - Solana blockchain interaction
- `yellowstone-grpc-client` - Real-time blockchain data
- `anyhow`, `thiserror` - Error handling
- `tracing` - Structured logging

### Frontend
- React 18+ with TypeScript
- Ant Design for UI components
- Axios for API communication

## Common Commands

### Development
```bash
# Backend development
cargo build                    # Build debug version
cargo build --release         # Build optimized version
cargo run                      # Run main application
cargo test                     # Run tests

# Frontend development
cd new2.0qianduan/wallet-admin-frontend
npm install                    # Install dependencies
npm run build                  # Build for production
npm run dev                    # Development server

# Redis (if running locally)
redis-server                   # Start Redis server
redis-cli                      # Redis command line interface
```

### Docker Deployment
```bash
# Build and run the complete system
docker build -f Dockerfile.all-in-one -t trading-system .
docker run -d --rm -p 8080:80 --name trading-system trading-system

# Load pre-built image
docker load -i "交易系统-20分钟自毁版-v1.0.tar"
docker run -d --rm -p 8080:80 --name trading-system <image-name>

# Management commands
docker ps                      # Check running containers
docker logs trading-system    # View logs
docker stop trading-system    # Stop container
```

### Configuration
- Backend config: `config.toml` files in respective project directories
- Frontend config: Environment variables and build-time configuration
- Docker: Multi-stage builds with Ubuntu 22.04 base image

## Architecture Patterns
- **Multi-stage Docker builds** for optimized production images
- **Protocol dispatcher pattern** for handling multiple blockchain protocols
- **Redis pub/sub** for real-time message processing
- **Microservices architecture** with shared types and utilities
- **Hot path optimization** for low-latency trading execution