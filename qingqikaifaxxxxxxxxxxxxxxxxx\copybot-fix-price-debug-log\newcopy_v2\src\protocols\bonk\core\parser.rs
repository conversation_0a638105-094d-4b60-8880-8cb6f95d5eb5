use serde::{Deserialize, Serialize};
use tracing::debug;

/// Bonk交易数据结构（100%复制bonk参考代码的TradingData结构）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BonkTradingData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

/// 解析taoli发布的Bonk数据格式：field": "value",
pub fn parse_bonk_data_from_redis(payload: &[u8]) -> Option<BonkTradingData> {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 检查是否是Bonk格式的数据
        if !data_str.contains("pool_state") || !data_str.contains("pool_base_vault") {
            return None;
        }

        let mut bonk_data = BonkTradingData {
            signature: String::new(),
            pool_state: String::new(),
            signer: String::new(),
            mint_address: String::new(),
            total_base_sell: 0,
            virtual_base: 0,
            virtual_quote: 0,
            real_base_before: 0,
            real_quote_before: 0,
            real_base_after: 0,
            real_quote_after: 0,
            amount_in: 0,
            amount_out: 0,
            protocol_fee: 0,
            platform_fee: 0,
            share_fee: 0,
            trade_direction: String::new(),
            pool_status: String::new(),
            price_before: 0.0,
            price_after: 0.0,
            slippage: 0.0,
            pool_base_vault: String::new(),
            pool_quote_vault: String::new(),
        };

        // 解析taoli的特殊格式：field": "value",
        for line in data_str.lines() {
            let line = line.trim();
            if line.is_empty() { continue; }

            // 查找 ": " 模式
            if let Some(colon_pos) = line.find("\": ") {
                let key = &line[..colon_pos];
                let value_part = &line[colon_pos + 3..]; // 跳过 ": "

                // 移除末尾的逗号和引号，并移除所有空白字符（包括换行符和空格）
                let value = value_part.trim_end_matches(',').trim_matches('"').replace(&[' ', '\n', '\r', '\t'][..], "");

                match key {
                    "signature" => bonk_data.signature = value.to_string(),
                    "pool_state" => bonk_data.pool_state = value.to_string(),
                    "signer" => bonk_data.signer = value.to_string(),
                    "mint_address" => bonk_data.mint_address = value.to_string(),
                    "trade_direction" => bonk_data.trade_direction = value.to_string(),
                    "pool_status" => bonk_data.pool_status = value.to_string(),
                    "pool_base_vault" => bonk_data.pool_base_vault = value.to_string(),
                    "pool_quote_vault" => bonk_data.pool_quote_vault = value.to_string(),
                    "total_base_sell" => bonk_data.total_base_sell = value.parse().unwrap_or(0),
                    "virtual_base" => bonk_data.virtual_base = value.parse().unwrap_or(0),
                    "virtual_quote" => bonk_data.virtual_quote = value.parse().unwrap_or(0),
                    "real_base_before" => bonk_data.real_base_before = value.parse().unwrap_or(0),
                    "real_quote_before" => bonk_data.real_quote_before = value.parse().unwrap_or(0),
                    "real_base_after" => bonk_data.real_base_after = value.parse().unwrap_or(0),
                    "real_quote_after" => bonk_data.real_quote_after = value.parse().unwrap_or(0),
                    "amount_in" => bonk_data.amount_in = value.parse().unwrap_or(0),
                    "amount_out" => bonk_data.amount_out = value.parse().unwrap_or(0),
                    "protocol_fee" => bonk_data.protocol_fee = value.parse().unwrap_or(0),
                    "platform_fee" => bonk_data.platform_fee = value.parse().unwrap_or(0),
                    "share_fee" => bonk_data.share_fee = value.parse().unwrap_or(0),
                    "price_before" => bonk_data.price_before = value.parse().unwrap_or(0.0),
                    "price_after" => bonk_data.price_after = value.parse().unwrap_or(0.0),
                    "slippage" => bonk_data.slippage = value.parse().unwrap_or(0.0),
                    _ => {} // 忽略未知字段
                }
            }
        }

        // 基本验证
        if bonk_data.mint_address == "unknown" || bonk_data.mint_address.is_empty() {
            return None;
        }

        if bonk_data.signature.is_empty() {
            return None;
        }

        debug!("成功解析Bonk交易数据: {} ({}) - mint: {}, 金额: {} -> {}, 价格: {:.8} -> {:.8}, 滑点: {:.2}%",
            bonk_data.signature, bonk_data.trade_direction, bonk_data.mint_address,
            bonk_data.amount_in, bonk_data.amount_out,
            bonk_data.price_before, bonk_data.price_after, bonk_data.slippage);

        Some(bonk_data)
    } else {
        None
    }
}

/// 检测是否为bonk数据
pub fn is_bonk_data(payload: &[u8]) -> bool {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 精确的bonk数据特征检测
        data_str.contains("pool_state") && 
        data_str.contains("pool_base_vault") && 
        data_str.contains("pool_quote_vault") &&
        data_str.contains("trade_direction")
    } else {
        false
    }
}