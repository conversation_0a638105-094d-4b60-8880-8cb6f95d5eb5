use serde::{Deserialize, Serialize};
use tracing::{debug, error};

/// Bonk交易数据结构（100%复制bonk参考代码的TradingData结构）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BonkTradingData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

/// 简单解析Bonk Redis数据 - 不做任何转换，直接返回原始数据结构
pub fn parse_bonk_data_from_redis(payload: &[u8]) -> Option<BonkTradingData> {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 直接尝试JSON解析
        match serde_json::from_str::<BonkTradingData>(data_str) {
            Ok(bonk_data) => {
                // 基本验证：跳过无效数据
                if bonk_data.mint_address == "unknown" || bonk_data.mint_address.is_empty() {
                    debug!("跳过未知Token地址的Bonk消息");
                    return None;
                }
                
                if bonk_data.signature.is_empty() {
                    debug!("跳过无效签名的Bonk消息");
                    return None;
                }
                
                debug!("成功解析Bonk数据: signature={}, mint={}, direction={}, amount_in={}, amount_out={}", 
                    bonk_data.signature, bonk_data.mint_address, bonk_data.trade_direction, 
                    bonk_data.amount_in, bonk_data.amount_out);
                
                Some(bonk_data)
            }
            Err(e) => {
                debug!("无法解析Bonk JSON数据: {}", e);
                None
            }
        }
    } else {
        error!("Bonk数据UTF-8解码失败");
        None
    }
}

/// 检测是否为bonk数据
pub fn is_bonk_data(payload: &[u8]) -> bool {
    if let Ok(data_str) = std::str::from_utf8(payload) {
        // 精确的bonk数据特征检测
        data_str.contains("pool_state") && 
        data_str.contains("pool_base_vault") && 
        data_str.contains("pool_quote_vault") &&
        data_str.contains("trade_direction")
    } else {
        false
    }
}